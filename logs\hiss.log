2025-07-02 23:10:40,761 [INFO] src.utils.llm: {"event": "Initialized Gemini LLM: gemini-1.5-flash", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:10:40.760055Z"}
2025-07-02 23:12:22,800 [INFO] src.utils.llm: {"event": "Initialized Gemini LLM: gemini-1.5-flash", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:12:22.800890Z"}
2025-07-02 23:12:22,967 [INFO] src.api.admin_dashboard: {"event": "Default admin users initialized", "logger": "src.api.admin_dashboard", "level": "info", "timestamp": "2025-07-02T18:12:22.967253Z"}
2025-07-02 23:12:31,125 [INFO] src.utils.llm: {"event": "Initialized Gemini LLM: gemini-1.5-flash", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:12:31.125980Z"}
2025-07-02 23:12:31,202 [INFO] src.api.admin_dashboard: {"event": "Default admin users initialized", "logger": "src.api.admin_dashboard", "level": "info", "timestamp": "2025-07-02T18:12:31.202303Z"}
2025-07-02 23:12:31,532 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:31,895 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:32,258 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:32,617 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:32,842 [INFO] src.api.main: {"event": "Starting HISS system...", "logger": "src.api.main", "level": "info", "timestamp": "2025-07-02T18:12:32.842069Z"}
2025-07-02 23:12:32,974 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:33,339 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:33,700 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:34,069 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:34,455 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:34,815 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:35,177 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:35,541 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:35,903 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:36,263 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:36,490 [ERROR] chromadb.telemetry.product.posthog: Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-07-02 23:12:36,492 [ERROR] chromadb.telemetry.product.posthog: Failed to send telemetry event ClientCreateCollectionEvent: capture() takes 1 positional argument but 3 were given
2025-07-02 23:12:36,506 [INFO] src.database.vector_store: {"event": "Vector store initialized successfully", "logger": "src.database.vector_store", "level": "info", "timestamp": "2025-07-02T18:12:36.506534Z"}
2025-07-02 23:12:36,506 [INFO] src.database.document_loader: {"event": "Initializing knowledge base...", "logger": "src.database.document_loader", "level": "info", "timestamp": "2025-07-02T18:12:36.506534Z"}
2025-07-02 23:12:36,508 [WARNING] src.database.document_loader: {"event": "Security protocols directory not found", "logger": "src.database.document_loader", "level": "warning", "timestamp": "2025-07-02T18:12:36.508046Z"}
2025-07-02 23:12:36,508 [WARNING] src.database.document_loader: {"event": "Visitor guidelines directory not found", "logger": "src.database.document_loader", "level": "warning", "timestamp": "2025-07-02T18:12:36.508046Z"}
2025-07-02 23:12:36,509 [WARNING] src.database.document_loader: {"event": "Emergency procedures directory not found", "logger": "src.database.document_loader", "level": "warning", "timestamp": "2025-07-02T18:12:36.509562Z"}
2025-07-02 23:12:36,509 [INFO] src.database.document_loader: {"event": "Loaded total of 0 documents to knowledge base", "logger": "src.database.document_loader", "level": "info", "timestamp": "2025-07-02T18:12:36.509562Z"}
2025-07-02 23:12:36,509 [WARNING] src.database.document_loader: {"event": "No documents found to initialize knowledge base", "logger": "src.database.document_loader", "level": "warning", "timestamp": "2025-07-02T18:12:36.509562Z"}
2025-07-02 23:12:36,509 [WARNING] src.api.main: {"event": "Knowledge base initialization failed", "logger": "src.api.main", "level": "warning", "timestamp": "2025-07-02T18:12:36.509562Z"}
2025-07-02 23:12:36,619 [INFO] watchfiles.main: 5 changes detected
2025-07-02 23:12:36,989 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:37,346 [INFO] watchfiles.main: 2 changes detected
2025-07-02 23:12:37,714 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:38,082 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:38,446 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:38,812 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:39,165 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:39,524 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:39,881 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:40,238 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:40,596 [INFO] watchfiles.main: 3 changes detected
2025-07-02 23:12:40,950 [INFO] watchfiles.main: 2 changes detected
2025-07-02 23:12:41,305 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:41,666 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:42,028 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:42,382 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:42,742 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:43,095 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:43,462 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:43,824 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:44,183 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:44,538 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:44,899 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:45,254 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:45,618 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:45,972 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:46,334 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:46,691 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:47,047 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:47,404 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:47,764 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:48,119 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:48,479 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:48,834 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:49,194 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:49,550 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:49,910 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:50,268 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:50,625 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:50,977 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:51,335 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:51,690 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:52,049 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:52,404 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:52,759 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:53,122 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:53,480 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:53,846 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:54,200 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:54,565 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:54,922 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:55,285 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:55,649 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:56,014 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:56,380 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:56,737 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:57,092 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:57,453 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:57,812 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:58,176 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:58,536 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:58,898 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:59,264 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:59,625 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:12:59,984 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:00,338 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:00,702 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:01,068 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:01,422 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:01,775 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:02,128 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:02,488 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:02,846 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:03,206 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:03,559 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:03,917 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:04,284 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:04,648 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:05,005 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:05,358 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:05,716 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:06,081 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:06,435 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:06,796 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:07,152 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:07,514 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:07,866 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:08,226 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:08,586 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:08,950 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:09,304 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:09,667 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:10,027 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:10,379 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:10,737 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:11,101 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:11,454 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:11,810 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:12,165 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:12,522 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:12,876 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:13,237 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:13,604 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:13,968 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:14,324 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:14,677 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:15,044 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:15,410 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:15,775 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:16,129 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:16,487 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:16,844 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:17,203 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:17,570 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:17,926 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:18,279 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:18,649 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:19,009 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:19,364 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:19,742 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:20,134 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:20,497 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:20,854 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:21,208 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:21,568 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:21,927 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:22,290 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:22,648 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:23,014 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:23,368 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:23,726 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:24,088 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:24,455 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:24,812 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:25,169 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:25,527 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:25,884 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:26,240 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:26,594 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:26,952 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:27,314 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:27,674 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:28,037 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:28,400 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:28,758 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:29,116 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:29,478 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:29,832 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:30,196 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:30,560 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:30,916 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:31,280 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:31,638 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:32,000 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:32,366 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:32,722 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:33,079 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:33,447 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:33,802 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:34,155 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:34,514 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:34,870 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:35,234 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:35,592 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:35,954 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:36,320 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:36,682 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:37,036 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:37,395 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:37,758 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:38,124 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:38,479 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:38,839 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:39,196 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:39,555 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:39,913 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:40,266 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:40,622 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:40,986 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:41,343 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:41,708 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:42,068 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:42,422 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:42,780 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:43,136 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:43,490 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:43,852 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:44,212 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:44,576 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:44,938 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:45,291 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:45,648 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:46,007 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:46,364 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:46,720 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:47,087 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:47,442 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:47,804 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:48,157 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:48,518 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:48,873 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:49,239 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:49,604 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:49,956 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:50,315 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:50,675 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:51,033 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:51,399 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:51,752 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:52,116 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:52,474 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:52,838 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:53,201 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:53,564 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:53,918 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:54,285 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:54,645 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:55,009 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:55,364 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:55,725 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:56,080 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:56,435 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:56,802 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:57,161 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:57,516 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:57,876 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:58,238 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:58,596 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:58,956 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:59,316 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:13:59,672 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:00,028 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:00,386 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:00,742 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:01,104 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:01,468 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:01,825 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:02,190 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:02,554 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:02,909 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:03,270 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:03,628 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:03,984 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:04,344 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:04,699 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:05,054 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:05,420 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:05,777 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:06,140 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:06,494 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:06,846 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:07,206 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:07,559 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:07,914 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:08,277 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:08,632 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:08,986 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:09,341 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:09,706 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:10,066 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:10,424 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:10,781 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:11,138 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:11,502 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:11,865 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:12,224 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:12,580 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:12,943 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:13,308 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:13,665 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:14,020 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:14,377 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:14,737 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:15,096 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:15,464 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:15,821 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:16,176 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:16,539 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:16,893 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:17,247 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:17,600 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:17,964 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:18,322 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:18,682 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:19,038 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:19,403 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:19,762 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:20,119 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:20,475 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:20,838 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:21,195 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:21,557 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:21,913 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:22,271 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:22,626 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:22,990 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:23,353 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:23,712 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:24,074 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:24,429 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:24,794 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:25,149 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:25,514 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:25,877 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:26,244 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:26,597 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:26,955 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:27,321 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:27,676 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:28,032 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:28,394 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:28,755 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:29,113 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:29,473 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:29,837 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:30,193 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:30,547 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:30,912 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:31,276 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:31,630 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:31,988 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:32,340 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:32,707 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:33,060 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:33,424 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:33,783 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:34,142 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:34,505 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:34,862 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:35,216 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:35,576 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:35,930 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:36,349 [INFO] watchfiles.main: 4 changes detected
2025-07-02 23:14:36,706 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:37,069 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:37,432 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:37,794 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:38,147 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:38,506 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:38,862 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:39,226 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:39,583 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:39,936 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:40,300 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:40,665 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:41,022 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:41,376 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:41,728 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:42,092 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:42,451 [INFO] watchfiles.main: 4 changes detected
2025-07-02 23:14:42,817 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:43,181 [INFO] watchfiles.main: 4 changes detected
2025-07-02 23:14:43,592 [INFO] watchfiles.main: 2 changes detected
2025-07-02 23:14:43,945 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:44,305 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:44,667 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:45,033 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:45,393 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:45,766 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:46,124 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:46,483 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:46,835 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:47,199 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:47,557 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:47,919 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:48,276 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:48,641 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:48,993 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:49,360 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:49,719 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:50,082 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:50,444 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:50,809 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:51,174 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:51,536 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:51,894 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:52,249 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:52,604 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:52,967 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:53,323 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:53,702 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:54,059 [INFO] watchfiles.main: 4 changes detected
2025-07-02 23:14:54,418 [INFO] watchfiles.main: 2 changes detected
2025-07-02 23:14:54,772 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:55,127 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:55,499 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:55,855 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:56,218 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:56,575 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:56,928 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:57,287 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:57,647 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:58,002 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:58,358 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:58,720 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:59,086 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:59,450 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:14:59,804 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:00,209 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:00,560 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:00,918 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:01,283 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:01,639 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:01,995 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:02,354 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:02,715 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:03,078 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:03,444 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:03,799 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:04,166 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:04,520 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:04,872 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:05,225 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:05,583 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:05,950 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:06,308 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:06,673 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:07,455 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:07,814 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:08,372 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:08,734 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:09,094 [INFO] watchfiles.main: 4 changes detected
2025-07-02 23:15:09,448 [INFO] watchfiles.main: 2 changes detected
2025-07-02 23:15:09,814 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:10,172 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:10,532 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:10,890 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:11,246 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:11,600 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:11,960 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:12,320 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:12,677 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:13,034 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:13,389 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:13,748 [INFO] watchfiles.main: 4 changes detected
2025-07-02 23:15:14,103 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:14,460 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:14,817 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:15,176 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:15,533 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:15,898 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:16,264 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:16,628 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:16,984 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:17,341 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:17,700 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:18,054 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:18,420 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:18,802 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:19,156 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:19,513 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:15:19,869 [INFO] watchfiles.main: 1 change detected
2025-07-02 23:34:40,394 [INFO] src.utils.llm: {"event": "Initialized Gemini LLM: gemini-1.5-flash", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:34:40.394930Z"}
2025-07-02 23:34:40,527 [INFO] src.api.admin_dashboard: {"event": "Default admin users initialized", "logger": "src.api.admin_dashboard", "level": "info", "timestamp": "2025-07-02T18:34:40.527309Z"}
2025-07-02 23:34:40,701 [INFO] src.api.main: {"event": "Starting HISS system...", "logger": "src.api.main", "level": "info", "timestamp": "2025-07-02T18:34:40.701110Z"}
2025-07-02 23:34:43,794 [ERROR] chromadb.telemetry.product.posthog: Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-07-02 23:34:43,797 [ERROR] chromadb.telemetry.product.posthog: Failed to send telemetry event ClientCreateCollectionEvent: capture() takes 1 positional argument but 3 were given
2025-07-02 23:34:43,807 [INFO] src.database.vector_store: {"event": "Vector store initialized successfully", "logger": "src.database.vector_store", "level": "info", "timestamp": "2025-07-02T18:34:43.805930Z"}
2025-07-02 23:34:43,807 [INFO] src.database.document_loader: {"event": "Initializing knowledge base...", "logger": "src.database.document_loader", "level": "info", "timestamp": "2025-07-02T18:34:43.807214Z"}
2025-07-02 23:34:43,808 [WARNING] src.database.document_loader: {"event": "Security protocols directory not found", "logger": "src.database.document_loader", "level": "warning", "timestamp": "2025-07-02T18:34:43.808591Z"}
2025-07-02 23:34:43,809 [WARNING] src.database.document_loader: {"event": "Visitor guidelines directory not found", "logger": "src.database.document_loader", "level": "warning", "timestamp": "2025-07-02T18:34:43.809801Z"}
2025-07-02 23:34:43,810 [WARNING] src.database.document_loader: {"event": "Emergency procedures directory not found", "logger": "src.database.document_loader", "level": "warning", "timestamp": "2025-07-02T18:34:43.810206Z"}
2025-07-02 23:34:43,811 [INFO] src.database.document_loader: {"event": "Loaded total of 0 documents to knowledge base", "logger": "src.database.document_loader", "level": "info", "timestamp": "2025-07-02T18:34:43.811247Z"}
2025-07-02 23:34:43,811 [WARNING] src.database.document_loader: {"event": "No documents found to initialize knowledge base", "logger": "src.database.document_loader", "level": "warning", "timestamp": "2025-07-02T18:34:43.811247Z"}
2025-07-02 23:34:43,812 [WARNING] src.api.main: {"event": "Knowledge base initialization failed", "logger": "src.api.main", "level": "warning", "timestamp": "2025-07-02T18:34:43.812265Z"}
2025-07-02 23:35:52,720 [INFO] interaction: {"interaction_type": "outdoor_greeting", "visitor_id": null, "message": "Good day! I'm here to assist visitors. What brings you here today?", "response": null, "system": "HISS Security Guard", "interaction_id": "fe9c780b-e9a8-478e-87ea-dbca3cdb93ce", "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T18:35:52.720715Z"}
2025-07-02 23:36:22,722 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:36:22.722857Z"}
2025-07-02 23:36:24,855 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:36:24.854179Z"}
2025-07-02 23:36:24,857 [INFO] src.utils.llm: {"prompt_length": 1141, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:36:24.857373Z"}
2025-07-02 23:36:24,859 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "fe9c780b-e9a8-478e-87ea-dbca3cdb93ce", "message": "I want to meet ali", "response": "Hi Badar, welcome!  It's a lovely day, isn't it?  You're looking to meet Ali?  Could you tell me a little more about why you're here to see him, please?  This helps me make sure I'm letting the right people in.", "system": "HISS Security Guard", "intent": "general", "sentiment": "neutral", "needs_permission": false, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T18:36:24.857373Z"}
2025-07-02 23:37:19,406 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:37:19.406332Z"}
2025-07-02 23:37:20,997 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:37:20.983099Z"}
2025-07-02 23:37:21,014 [INFO] src.utils.llm: {"prompt_length": 1174, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T18:37:21.014943Z"}
2025-07-02 23:37:21,023 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "fe9c780b-e9a8-478e-87ea-dbca3cdb93ce", "message": "i am his old frient i wna tto meet after long time ", "response": "Hi Badar, it's nice to meet you!  I understand you're an old friend of the homeowner and want to visit.  Could you please tell me the homeowner's name so I can verify your identity and let them know you're here?  Also, could you tell me a little bit more about when you last saw them, or perhaps a detail they might remember about you? This will just help me confirm your visit.  I want to make sure I'm letting the right people in.", "system": "HISS Security Guard", "intent": "general", "sentiment": "neutral", "needs_permission": false, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T18:37:21.023350Z"}
2025-07-03 00:28:19,091 [INFO] interaction: {"interaction_type": "outdoor_greeting", "visitor_id": null, "message": "Hi there! I'm the digital security guard. How may I assist you?", "response": null, "system": "HISS Security Guard", "interaction_id": "7f3e4ddb-dee7-469f-bac0-7d1060c1bfc0", "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T19:28:19.091762Z"}
2025-07-03 00:29:13,263 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:29:13.263497Z"}
2025-07-03 00:29:14,754 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:29:14.754534Z"}
2025-07-03 00:29:14,758 [INFO] src.utils.llm: {"prompt_length": 1197, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:29:14.756616Z"}
2025-07-03 00:29:14,768 [INFO] src.api.subadmin_panel: {"event": "Permission request created: b7b577df-4913-4a01-835f-9e66f34576a7 for visitor Elecrtical", "logger": "src.api.subadmin_panel", "level": "info", "timestamp": "2025-07-02T19:29:14.768191Z"}
2025-07-03 00:29:14,770 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "7f3e4ddb-dee7-469f-bac0-7d1060c1bfc0", "message": "I want to go to home for checking meter reading give me permission", "response": "Hello Elecrtical!  Thanks for letting me know you're here for a meter reading.  To ensure I can grant you access, could you please tell me a little more about your company and provide any identification or work order number you might have?  This helps us maintain the security of the home.  I'll need to verify your identity before I can let you in.\n\nI've sent your request to our security team for approval. Please wait a moment while they review it.", "system": "HISS Security Guard", "intent": "home_visit", "sentiment": "neutral", "needs_permission": true, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T19:29:14.770229Z"}
2025-07-03 00:32:24,340 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:32:24.340261Z"}
2025-07-03 00:32:26,212 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:32:26.212981Z"}
2025-07-03 00:32:26,215 [INFO] src.utils.llm: {"prompt_length": 1274, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:32:26.215077Z"}
2025-07-03 00:32:26,217 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "7f3e4ddb-dee7-469f-bac0-7d1060c1bfc0", "message": "please give me permission i am form fesco govt comapny you houe gold member call me for checking meter reading that is stoped pelase hurry up", "response": "Hello Elecrtical, thank you for contacting me.  I understand you're from FESCO, the government company, and you're here for a meter reading.  Could you please provide me with your employee ID number and the name of the home's resident whose meter you need to check?  Also, could you confirm the address you have on file to ensure it matches the address of this property?  \n\nI need to verify your identity and purpose before granting access to the home. This is standard procedure to ensure the safety and security of the residents. Once I have this information, I'll contact the homeowner to confirm your visit.  Please be patient while I do so.  This may take a few moments.", "system": "HISS Security Guard", "intent": "home_visit", "sentiment": "positive", "needs_permission": true, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T19:32:26.217102Z"}
2025-07-03 00:33:29,062 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:33:29.062706Z"}
2025-07-03 00:33:30,438 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:33:30.438473Z"}
2025-07-03 00:33:30,439 [INFO] src.utils.llm: {"prompt_length": 1201, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:33:30.439830Z"}
2025-07-03 00:33:30,442 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "7f3e4ddb-dee7-469f-bac0-7d1060c1bfc0", "message": "my id is 15 and am from fesco i have a lot work plese give me permission", "response": "Hello Elecrtical!  Thanks for letting me know you're here.  I understand you're from FesCo with ID 15 and have work to do.  To ensure I can help you efficiently and safely, could you please tell me a little more about the nature of your work today?  What specifically needs to be done at this residence?  Knowing this will help me determine if I can grant you access.", "system": "HISS Security Guard", "intent": "general", "sentiment": "neutral", "needs_permission": false, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T19:33:30.442397Z"}
2025-07-03 00:34:10,386 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:34:10.386307Z"}
2025-07-03 00:34:11,436 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:34:11.434683Z"}
2025-07-03 00:34:11,436 [INFO] src.utils.llm: {"prompt_length": 1204, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:34:11.436701Z"}
2025-07-03 00:34:11,438 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "7f3e4ddb-dee7-469f-bac0-7d1060c1bfc0", "message": "there are lot of home that need reading chack please gran t me permission ", "response": "\"Hi Elecrtical, thanks for letting me know you're here.  I understand you're doing meter readings for homes in the area.  To ensure I grant the correct access, could you please tell me which specific home you need to access?  Knowing the address or the homeowner's name would be very helpful.\"", "system": "HISS Security Guard", "intent": "general", "sentiment": "positive", "needs_permission": false, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T19:34:11.438716Z"}
2025-07-03 00:35:39,876 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:35:39.876160Z"}
2025-07-03 00:35:40,997 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:35:40.997783Z"}
2025-07-03 00:35:40,999 [INFO] src.utils.llm: {"prompt_length": 1205, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:35:40.997783Z"}
2025-07-03 00:35:41,002 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "7f3e4ddb-dee7-469f-bac0-7d1060c1bfc0", "message": "yes owners name is ali and house number 45 that is thiis pleae open the door", "response": "Hello Elecrtical!  Thanks for letting me know the house number and owner's name.  To ensure I'm letting the right person in, could you please tell me a little more about why you're visiting Ali at number 45 today?  Are you here for a delivery, a scheduled appointment, or something else?", "system": "HISS Security Guard", "intent": "general", "sentiment": "neutral", "needs_permission": false, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T19:35:40.999816Z"}
2025-07-03 00:36:16,119 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:36:16.119464Z"}
2025-07-03 00:36:17,145 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:36:17.145770Z"}
2025-07-03 00:36:17,147 [INFO] src.utils.llm: {"prompt_length": 1150, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:36:17.147543Z"}
2025-07-03 00:36:17,148 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "7f3e4ddb-dee7-469f-bac0-7d1060c1bfc0", "message": "scheduled appointment", "response": "Hi Elecrtical, thanks for letting me know you have a scheduled appointment.  Could you tell me a little more about it, please?  Who is the appointment with, and what company are you with?  This will help me get the right person to assist you.", "system": "HISS Security Guard", "intent": "general", "sentiment": "neutral", "needs_permission": false, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T19:36:17.148724Z"}
2025-07-03 00:36:52,346 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:36:52.346335Z"}
2025-07-03 00:36:53,494 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:36:53.493655Z"}
2025-07-03 00:36:53,494 [INFO] src.utils.llm: {"prompt_length": 1160, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:36:53.494165Z"}
2025-07-03 00:36:53,496 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "7f3e4ddb-dee7-469f-bac0-7d1060c1bfc0", "message": "please register me as visitors", "response": "Hi Elecrtical, thanks for letting me know you're here!  To register you as a visitor, I just need a little more information to ensure everything is secure.  Could you tell me who you're visiting, and what the purpose of your visit is today?  Knowing this will help me get you connected with the right person quickly.", "system": "HISS Security Guard", "intent": "general", "sentiment": "positive", "needs_permission": false, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T19:36:53.494165Z"}
2025-07-03 00:37:34,191 [INFO] src.utils.llm: {"prompts_count": 1, "event": "LLM interaction started", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:37:34.191993Z"}
2025-07-03 00:37:35,434 [INFO] src.utils.llm: {"event": "LLM interaction completed", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:37:35.434220Z"}
2025-07-03 00:37:35,436 [INFO] src.utils.llm: {"prompt_length": 1173, "event": "Generated LLM response", "logger": "src.utils.llm", "level": "info", "timestamp": "2025-07-02T19:37:35.436244Z"}
2025-07-03 00:37:35,438 [INFO] interaction: {"interaction_type": "outdoor_conversation", "visitor_id": "7f3e4ddb-dee7-469f-bac0-7d1060c1bfc0", "message": "to check meter reading as i already tell you", "response": "Hi Elecrtical, thanks for letting me know you're here to check the meter reading.  I understand you mentioned this earlier.  To ensure I can grant you access, could you please tell me your company name and provide me with your ID number or any other identifying information you're authorized to share?  This is just a standard security procedure.  Once I verify your details, I can let the homeowner know you're here and awaiting access.", "system": "HISS Security Guard", "intent": "general", "sentiment": "neutral", "needs_permission": false, "event": "Visitor interaction", "logger": "interaction", "level": "info", "timestamp": "2025-07-02T19:37:35.438288Z"}
