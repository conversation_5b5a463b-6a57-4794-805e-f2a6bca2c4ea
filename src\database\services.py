"""
Database service classes for HISS
Handles all database operations with proper async/await patterns
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, desc
from sqlalchemy.orm import selectinload

from .models import (
    User, Visitor, ConversationSession, ConversationMessage,
    OutdoorInteraction, SecurityDecision, PermissionRequest,
    AdminAction, SystemAlert
)
from .config import get_db
from ..utils.logger import get_logger

logger = get_logger(__name__)


class UserService:
    """Service for user management operations"""
    
    @staticmethod
    async def create_user(db: AsyncSession, user_data: Dict[str, Any]) -> User:
        """Create a new user"""
        user = User(**user_data)
        db.add(user)
        await db.commit()
        await db.refresh(user)
        return user
    
    @staticmethod
    async def get_user_by_id(db: AsyncSession, user_id: str) -> Optional[User]:
        """Get user by ID"""
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
        """Get user by username"""
        result = await db.execute(select(User).where(User.username == username))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_all_users(db: AsyncSession) -> List[User]:
        """Get all users"""
        result = await db.execute(select(User).order_by(User.created_at.desc()))
        return result.scalars().all()
    
    @staticmethod
    async def update_last_login(db: AsyncSession, user_id: str):
        """Update user's last login time"""
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(last_login=datetime.utcnow())
        )
        await db.commit()


class VisitorService:
    """Service for visitor management operations"""
    
    @staticmethod
    async def create_visitor(db: AsyncSession, visitor_data: Dict[str, Any]) -> Visitor:
        """Create a new visitor"""
        visitor = Visitor(**visitor_data)
        db.add(visitor)
        await db.commit()
        await db.refresh(visitor)
        return visitor
    
    @staticmethod
    async def get_visitor_by_id(db: AsyncSession, visitor_id: str) -> Optional[Visitor]:
        """Get visitor by ID"""
        result = await db.execute(
            select(Visitor)
            .options(selectinload(Visitor.conversations))
            .where(Visitor.id == visitor_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_all_visitors(db: AsyncSession, status: Optional[str] = None) -> List[Visitor]:
        """Get all visitors, optionally filtered by status"""
        query = select(Visitor).order_by(Visitor.created_at.desc())
        if status:
            query = query.where(Visitor.status == status)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    @staticmethod
    async def update_visitor_status(db: AsyncSession, visitor_id: str, status: str):
        """Update visitor status"""
        await db.execute(
            update(Visitor)
            .where(Visitor.id == visitor_id)
            .values(status=status)
        )
        await db.commit()


class ConversationService:
    """Service for conversation management operations"""
    
    @staticmethod
    async def create_session(db: AsyncSession, session_data: Dict[str, Any]) -> ConversationSession:
        """Create a new conversation session"""
        session = ConversationSession(**session_data)
        db.add(session)
        await db.commit()
        await db.refresh(session)
        return session
    
    @staticmethod
    async def create_message(db: AsyncSession, message_data: Dict[str, Any]) -> ConversationMessage:
        """Create a new conversation message"""
        message = ConversationMessage(**message_data)
        db.add(message)
        
        # Update session message count if session_id provided
        if message.session_id:
            await db.execute(
                update(ConversationSession)
                .where(ConversationSession.id == message.session_id)
                .values(total_messages=ConversationSession.total_messages + 1)
            )
        
        await db.commit()
        await db.refresh(message)
        return message
    
    @staticmethod
    async def get_all_conversations(
        db: AsyncSession, 
        limit: int = 50, 
        offset: int = 0,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> List[ConversationMessage]:
        """Get all conversation messages with pagination and filtering"""
        query = select(ConversationMessage).order_by(ConversationMessage.timestamp.desc())
        
        if date_from:
            query = query.where(ConversationMessage.timestamp >= date_from)
        if date_to:
            query = query.where(ConversationMessage.timestamp <= date_to)
        
        query = query.offset(offset).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    @staticmethod
    async def get_visitor_conversations(db: AsyncSession, visitor_id: str) -> List[ConversationMessage]:
        """Get all conversations for a specific visitor"""
        result = await db.execute(
            select(ConversationMessage)
            .where(ConversationMessage.visitor_id == visitor_id)
            .order_by(ConversationMessage.timestamp.asc())
        )
        return result.scalars().all()
    
    @staticmethod
    async def get_session_by_id(db: AsyncSession, session_id: str) -> Optional[ConversationSession]:
        """Get conversation session by ID"""
        result = await db.execute(
            select(ConversationSession)
            .options(selectinload(ConversationSession.messages))
            .where(ConversationSession.id == session_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def end_session(db: AsyncSession, session_id: str, final_decision: str = None):
        """End a conversation session"""
        await db.execute(
            update(ConversationSession)
            .where(ConversationSession.id == session_id)
            .values(
                ended_at=datetime.utcnow(),
                final_decision=final_decision
            )
        )
        await db.commit()


class OutdoorInteractionService:
    """Service for outdoor interaction operations"""
    
    @staticmethod
    async def create_interaction(db: AsyncSession, interaction_data: Dict[str, Any]) -> OutdoorInteraction:
        """Create a new outdoor interaction"""
        interaction = OutdoorInteraction(**interaction_data)
        db.add(interaction)
        await db.commit()
        await db.refresh(interaction)
        return interaction
    
    @staticmethod
    async def get_interaction_by_id(db: AsyncSession, interaction_id: str) -> Optional[OutdoorInteraction]:
        """Get outdoor interaction by ID"""
        result = await db.execute(
            select(OutdoorInteraction).where(OutdoorInteraction.id == interaction_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def update_interaction(db: AsyncSession, interaction_id: str, update_data: Dict[str, Any]):
        """Update outdoor interaction"""
        await db.execute(
            update(OutdoorInteraction)
            .where(OutdoorInteraction.id == interaction_id)
            .values(**update_data)
        )
        await db.commit()
    
    @staticmethod
    async def end_interaction(db: AsyncSession, interaction_id: str, outcome: str):
        """End an outdoor interaction"""
        await db.execute(
            update(OutdoorInteraction)
            .where(OutdoorInteraction.id == interaction_id)
            .values(
                interaction_end=datetime.utcnow(),
                final_outcome=outcome
            )
        )
        await db.commit()


class PermissionRequestService:
    """Service for permission request operations"""
    
    @staticmethod
    async def create_request(db: AsyncSession, request_data: Dict[str, Any]) -> PermissionRequest:
        """Create a new permission request"""
        request = PermissionRequest(**request_data)
        db.add(request)
        await db.commit()
        await db.refresh(request)
        return request
    
    @staticmethod
    async def get_pending_requests(db: AsyncSession) -> List[PermissionRequest]:
        """Get all pending permission requests"""
        result = await db.execute(
            select(PermissionRequest)
            .where(PermissionRequest.status == "pending")
            .order_by(PermissionRequest.priority.desc(), PermissionRequest.requested_at.asc())
        )
        return result.scalars().all()
    
    @staticmethod
    async def get_request_by_id(db: AsyncSession, request_id: str) -> Optional[PermissionRequest]:
        """Get permission request by ID"""
        result = await db.execute(
            select(PermissionRequest).where(PermissionRequest.id == request_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def update_request_status(
        db: AsyncSession, 
        request_id: str, 
        status: str, 
        reviewed_by: str,
        decision_reason: str
    ):
        """Update permission request status"""
        await db.execute(
            update(PermissionRequest)
            .where(PermissionRequest.id == request_id)
            .values(
                status=status,
                reviewed_by=reviewed_by,
                reviewed_at=datetime.utcnow(),
                decision_reason=decision_reason
            )
        )
        await db.commit()
    
    @staticmethod
    async def get_request_history(db: AsyncSession, limit: int = 50) -> List[PermissionRequest]:
        """Get permission request history"""
        result = await db.execute(
            select(PermissionRequest)
            .order_by(PermissionRequest.requested_at.desc())
            .limit(limit)
        )
        return result.scalars().all()


class SecurityDecisionService:
    """Service for security decision operations"""
    
    @staticmethod
    async def create_decision(db: AsyncSession, decision_data: Dict[str, Any]) -> SecurityDecision:
        """Create a new security decision"""
        decision = SecurityDecision(**decision_data)
        db.add(decision)
        await db.commit()
        await db.refresh(decision)
        return decision
    
    @staticmethod
    async def get_all_decisions(db: AsyncSession) -> List[SecurityDecision]:
        """Get all security decisions"""
        result = await db.execute(
            select(SecurityDecision).order_by(SecurityDecision.decided_at.desc())
        )
        return result.scalars().all()
    
    @staticmethod
    async def get_decision_by_id(db: AsyncSession, decision_id: str) -> Optional[SecurityDecision]:
        """Get security decision by ID"""
        result = await db.execute(
            select(SecurityDecision).where(SecurityDecision.id == decision_id)
        )
        return result.scalar_one_or_none()


class AdminActionService:
    """Service for admin action logging"""
    
    @staticmethod
    async def log_action(db: AsyncSession, action_data: Dict[str, Any]) -> AdminAction:
        """Log an admin action"""
        action = AdminAction(**action_data)
        db.add(action)
        await db.commit()
        await db.refresh(action)
        return action
    
    @staticmethod
    async def get_action_history(db: AsyncSession, limit: int = 50) -> List[AdminAction]:
        """Get admin action history"""
        result = await db.execute(
            select(AdminAction)
            .order_by(AdminAction.timestamp.desc())
            .limit(limit)
        )
        return result.scalars().all()


class SystemAlertService:
    """Service for system alert operations"""
    
    @staticmethod
    async def create_alert(db: AsyncSession, alert_data: Dict[str, Any]) -> SystemAlert:
        """Create a new system alert"""
        alert = SystemAlert(**alert_data)
        db.add(alert)
        await db.commit()
        await db.refresh(alert)
        return alert
    
    @staticmethod
    async def get_alerts(db: AsyncSession, unread_only: bool = False) -> List[SystemAlert]:
        """Get system alerts"""
        query = select(SystemAlert).order_by(SystemAlert.created_at.desc())
        if unread_only:
            query = query.where(SystemAlert.acknowledged == False)
        
        result = await db.execute(query.limit(20))
        return result.scalars().all()
    
    @staticmethod
    async def acknowledge_alert(db: AsyncSession, alert_id: str, acknowledged_by: str):
        """Acknowledge a system alert"""
        await db.execute(
            update(SystemAlert)
            .where(SystemAlert.id == alert_id)
            .values(
                acknowledged=True,
                acknowledged_by=acknowledged_by,
                acknowledged_at=datetime.utcnow()
            )
        )
        await db.commit()
