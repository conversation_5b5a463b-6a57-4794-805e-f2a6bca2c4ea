"""
Test database connection with proper async driver
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_connection():
    """Test database connection"""
    try:
        # Test basic asyncpg connection
        import asyncpg
        
        # Database URL
        database_url = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
        
        print("🔗 Testing direct asyncpg connection...")
        
        # Test direct connection
        conn = await asyncpg.connect(database_url)
        result = await conn.fetchval("SELECT 1")
        await conn.close()
        
        print(f"✅ Direct asyncpg connection successful: {result}")
        
        # Test SQLAlchemy async connection
        print("\n🔗 Testing SQLAlchemy async connection...")

        from sqlalchemy.ext.asyncio import create_async_engine
        from sqlalchemy import text

        # Convert to async URL and handle SSL
        import urllib.parse
        parsed = urllib.parse.urlparse(database_url)

        # Remove SSL parameters from query string
        query_params = urllib.parse.parse_qs(parsed.query)
        query_params.pop('sslmode', None)
        query_params.pop('channel_binding', None)

        # Rebuild URL without SSL parameters
        new_query = urllib.parse.urlencode(query_params, doseq=True)
        new_parsed = parsed._replace(query=new_query)
        clean_url = urllib.parse.urlunparse(new_parsed)

        # Convert to async format
        async_url = clean_url.replace("postgresql://", "postgresql+asyncpg://", 1)

        # SSL configuration
        connect_args = {
            "ssl": "require",
            "server_settings": {
                "application_name": "HISS_Test",
            }
        }

        engine = create_async_engine(async_url, echo=False, connect_args=connect_args)

        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            value = result.scalar()

        await engine.dispose()

        print(f"✅ SQLAlchemy async connection successful: {value}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install: pip install asyncpg")
        return False
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    if success:
        print("\n🎉 Database connection test passed!")
    else:
        print("\n💥 Database connection test failed!")
    
    sys.exit(0 if success else 1)
