# HISS - Human Interactive Security System

🛡️ **An Agentic RAG (Retrieval-Augmented Generation) system designed to act as an intelligent security guard with human-like interaction capabilities.**

## Overview

HISS is a comprehensive security management system that combines the power of:
- **Langchain & Langgraph** for agentic AI workflows
- **Google Gemini API** for advanced language understanding
- **ChromaDB** for intelligent document retrieval
- **FastAPI** for robust API endpoints
- **Real-time chat interface** for human-like interactions

## Key Features

### 🤖 Intelligent Security Guard
- **Automated visitor screening** with confidence scoring
- **Natural language conversations** with visitors
- **Context-aware decision making** based on security protocols
- **Real-time threat assessment** and response

### 👑 Admin Hierarchy System
- **Admin Dashboard** with complete conversation history access
- **SubAdmin Panel** with visitor permission approval authority
- **Role-based permissions** and access control
- **Real-time notifications** and alert system

### 🏠 Outdoor Interaction Bot
- **Human-like conversations** with visitors outside the home
- **Intent detection** and visitor purpose analysis
- **Automatic permission requests** to SubAdmin when home access needed
- **Real-time status updates** throughout interaction process

### 📚 Knowledge-Powered Decisions
- **RAG-based information retrieval** from security documents
- **Dynamic policy application** based on current protocols
- **Continuous learning** from interactions and decisions
- **Comprehensive audit trail** of all activities

### 🔄 Agentic Workflow
- **Multi-step decision processes** using Langgraph
- **Conditional logic flows** for complex scenarios
- **Human-in-the-loop** capabilities for escalation
- **Automated documentation** and reporting

### 💬 Human-Like Interactions
- **Natural conversation flow** with visitors
- **Contextual responses** based on visitor information
- **Helpful suggestions** and guidance
- **Multi-modal communication** support

## Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| **LLM** | Google Gemini API | Natural language understanding and generation |
| **Framework** | Langchain + Langgraph | Agentic AI workflows and document processing |
| **API** | FastAPI | REST endpoints and WebSocket support |
| **Vector DB** | ChromaDB | Document storage and similarity search |
| **Models** | Pydantic | Data validation and serialization |
| **Chat** | WebSocket + HTML | Real-time interactive interface |

## Quick Start

### 1. Prerequisites
- Python 3.10+
- Google Gemini API key
- Git

### 2. Installation
```bash
git clone <repository-url>
cd hiss-rag
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -e .
```

### 3. Configuration
```bash
cp .env.example .env
# Edit .env with your Google Gemini API key
```

### 4. Initialize System
```bash
python scripts/initialize_hiss.py
```

### 5. Start HISS
```bash
python main.py
```

### 6. Access Interfaces
- **Outdoor Chat**: http://localhost:8000/outdoor/chat
- **Admin Dashboard**: http://localhost:8000/admin/dashboard
- **SubAdmin Panel**: http://localhost:8000/subadmin/panel
- **API Documentation**: http://localhost:8000/docs
- **Indoor Chat**: http://localhost:8000/chat
- **Health Check**: http://localhost:8000/health

## Project Structure

```
hiss-rag/
├── src/
│   ├── api/              # FastAPI endpoints and WebSocket chat
│   ├── agents/           # Langgraph security agent workflows
│   ├── database/         # ChromaDB vector store and document loaders
│   ├── models/           # Pydantic data models
│   ├── prompts/          # LLM prompt templates
│   └── utils/            # Configuration, logging, and utilities
├── data/
│   ├── documents/        # Security protocols and guidelines
│   ├── fake_data/        # Generated test data
│   └── chroma_db/        # Vector database storage
├── tests/                # Unit and integration tests
├── examples/             # Usage examples and demo scripts
├── docs/                 # Comprehensive documentation
├── scripts/              # Setup and utility scripts
└── logs/                 # Application logs
```

## Core Components

### 🔐 Security Agent
The heart of HISS - an intelligent agent that:
- Analyzes visitor information
- Retrieves relevant security protocols
- Makes informed access decisions
- Generates human-like responses
- Handles complex security scenarios

### 📊 Vector Knowledge Base
Powered by ChromaDB:
- Stores security protocols and guidelines
- Enables semantic search of documents
- Provides context for decision making
- Supports real-time document updates

### 🌐 API & Chat Interface
Comprehensive interaction layer:
- RESTful API for system integration
- WebSocket chat for real-time conversations
- HTML interface for direct user interaction
- Comprehensive logging and monitoring

## Usage Examples

### Register and Screen a Visitor
```python
from examples.python_client import HissClient

client = HissClient()

# Register visitor
visitor_data = {
    "name": "John Doe",
    "purpose": "Business meeting",
    "company": "TechCorp",
    "contact_email": "<EMAIL>"
}
registration = client.register_visitor(visitor_data)
visitor_id = registration["data"]["visitor_id"]

# Screen for access
screening = client.screen_visitor(visitor_id)
decision = screening["data"]["decision"]  # ALLOW or DENY
confidence = screening["data"]["confidence"]  # 0.0 to 1.0
reason = screening["data"]["reason"]  # Explanation
```

### Interactive Chat
```python
# Chat with the security system
response = client.chat(
    message="Hello, I need help finding the conference room",
    visitor_id=visitor_id
)
print(response["response"])  # AI-generated helpful response
```

### Run Complete Demo
```bash
python examples/demo_script.py
```

## Security Features

### 🔍 Visitor Screening
- **Multi-factor analysis** of visitor information
- **Risk assessment** based on purpose and background
- **Appointment verification** and validation
- **Real-time decision making** with confidence scoring

### 🚨 Threat Detection
- **Suspicious behavior identification**
- **Unauthorized access attempts**
- **Emergency situation handling**
- **Automatic alert generation**

### 📋 Compliance & Auditing
- **Complete interaction logging**
- **Decision audit trails**
- **Policy compliance checking**
- **Regulatory reporting support**

## Configuration

Key configuration options in `.env`:

```env
# Core Settings
GOOGLE_API_KEY=your_gemini_api_key
SYSTEM_NAME=HISS Security Guard
SECURITY_LEVEL=high

# Model Settings
MODEL_NAME=gemini-1.5-flash
MODEL_TEMPERATURE=0.7
MAX_TOKENS=1000

# Database Settings
CHROMA_DB_PATH=./data/chroma_db
CHROMA_COLLECTION_NAME=hiss_security_knowledge

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
```

## Testing

Run the test suite:
```bash
pytest tests/
```

Run with coverage:
```bash
pytest tests/ --cov=src --cov-report=html
```

## Documentation

- **[Setup Guide](docs/SETUP_GUIDE.md)** - Detailed installation and configuration
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Complete API reference
- **[Examples](examples/)** - Usage examples and demo scripts

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or contributions:
- Check the documentation in `docs/`
- Review existing issues
- Create a new issue with detailed information
- Contact the development team

---

**Built with ❤️ using Langchain, Langgraph, FastAPI, ChromaDB, and Google Gemini API**