"""
Database initialization script for HISS
Creates tables and sets up initial data
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.database.config import init_db, check_db_connection
from src.database.services import UserService
from src.utils.logger import get_logger
from src.database.config import get_db

logger = get_logger(__name__)


async def create_default_users():
    """Create default admin and subadmin users"""
    try:
        async for db in get_db():
            # Check if admin already exists
            admin = await UserService.get_user_by_username(db, "admin")
            if not admin:
                admin_data = {
                    "username": "admin",
                    "email": "<EMAIL>",
                    "full_name": "System Administrator",
                    "role": "admin",
                    "password_hash": "8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918",  # admin123
                    "department": "Security",
                    "permissions": [
                        "view_all_conversations",
                        "manage_users", 
                        "approve_visitors",
                        "system_settings",
                        "view_analytics",
                        "emergency_override"
                    ]
                }
                admin = await UserService.create_user(db, admin_data)
                logger.info(f"Created admin user: {admin.username}")
            
            # Check if subadmin already exists
            subadmin = await UserService.get_user_by_username(db, "subadmin")
            if not subadmin:
                subadmin_data = {
                    "username": "subadmin",
                    "email": "<EMAIL>",
                    "full_name": "Security Supervisor",
                    "role": "subadmin",
                    "password_hash": "ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f",  # subadmin123
                    "department": "Security",
                    "permissions": [
                        "approve_visitors",
                        "view_analytics"
                    ]
                }
                subadmin = await UserService.create_user(db, subadmin_data)
                logger.info(f"Created subadmin user: {subadmin.username}")
            
            break  # Exit the async generator
            
    except Exception as e:
        logger.error(f"Failed to create default users: {e}")
        raise


async def main():
    """Main initialization function"""
    print("🗄️  HISS Database Initialization")
    print("=" * 50)
    
    try:
        # Check database connection
        print("\n1. Checking database connection...")
        if await check_db_connection():
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed")
            return False
        
        # Initialize database tables
        print("\n2. Creating database tables...")
        if await init_db():
            print("✅ Database tables created successfully")
        else:
            print("❌ Failed to create database tables")
            return False
        
        # Create default users
        print("\n3. Creating default users...")
        await create_default_users()
        print("✅ Default users created successfully")
        
        print("\n🎉 Database initialization completed successfully!")
        print("\nDefault credentials:")
        print("  Admin: admin / admin123")
        print("  SubAdmin: subadmin / subadmin123")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Database initialization failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
