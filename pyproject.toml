[project]
name = "hiss-rag"
version = "0.1.0"
description = "HISS - Human Interactive Security System: An Agentic RAG system for intelligent security management"
readme = "README_HISS.md"
requires-python = ">=3.10"
dependencies = [
    # Core Framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",

    # <PERSON><PERSON><PERSON><PERSON> and LangGraph
    "langchain>=0.1.0",
    "langchain-community>=0.0.10",
    "langchain-google-genai>=0.0.6",
    "langgraph>=0.0.20",

    # Vector Database
    "chromadb>=0.4.18",

    # Google Gemini API
    "google-generativeai>=0.3.0",

    # Document Processing
    "pypdf>=3.17.0",
    "python-docx>=1.1.0",
    "python-multipart>=0.0.6",

    # Data Models and Validation
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",

    # Utilities
    "python-dotenv>=1.0.0",
    "faker>=20.1.0",
    "requests>=2.31.0",

    # Async Support
    "aiofiles>=23.2.1",

    # Logging and Monitoring
    "structlog>=23.2.0",

    # Development Tools
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
