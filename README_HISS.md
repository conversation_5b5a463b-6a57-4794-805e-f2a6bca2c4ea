# HISS - Human Interactive Security System

An Agentic RAG (Retrieval-Augmented Generation) system designed to act as an intelligent security guard with human-like interaction capabilities.

## Features

- **Agentic AI Security Guard**: Intelligent decision-making for visitor access control
- **Human-like Interactions**: Natural conversation capabilities with visitors
- **RAG-powered Knowledge**: ChromaDB vector store for security protocols and visitor information
- **Real-time Decision Making**: Langgraph-based workflow for complex security scenarios
- **RESTful API**: FastAPI-based endpoints for system integration
- **Comprehensive Logging**: Full audit trail of all security decisions and interactions

## Technology Stack

- **LLM**: Google Gemini API
- **Framework**: Langchain + Langgraph
- **API**: FastAPI
- **Vector Database**: ChromaDB
- **Models**: Pydantic
- **Document Processing**: Langchain Document Loaders

## Project Structure

```
hiss-rag/
├── src/
│   ├── api/           # FastAPI endpoints
│   ├── agents/        # Langgraph agents and workflows
│   ├── database/      # ChromaDB vector store
│   ├── models/        # Pydantic models
│   ├── prompts/       # Prompt templates
│   └── utils/         # Helper functions
├── data/              # Sample data and documents
├── tests/             # Unit and integration tests
├── logs/              # Application logs
└── docs/              # Documentation
```

## Quick Start

1. Clone the repository
2. Copy `.env.example` to `.env` and configure your API keys
3. Install dependencies: `pip install -e .`
4. Run the system: `python main.py`
5. Access the API at `http://localhost:8000`

## Security Features

- Visitor screening and authorization
- Real-time threat assessment
- Interactive visitor communication
- Access log management
- Emergency response protocols
