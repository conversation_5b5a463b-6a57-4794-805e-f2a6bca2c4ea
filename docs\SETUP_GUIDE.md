# HISS Setup Guide

## Prerequisites

- Python 3.10 or higher
- Google Gemini API key
- Git (for cloning the repository)

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd hiss-rag
```

### 2. Create Virtual Environment

```bash
python -m venv venv

# On Windows
venv\Scripts\activate

# On macOS/Linux
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -e .
```

### 4. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` file with your configuration:

```env
# Google Gemini API Configuration
GOOGLE_API_KEY=your_actual_gemini_api_key_here

# ChromaDB Configuration
CHROMA_DB_PATH=./data/chroma_db
CHROMA_COLLECTION_NAME=hiss_security_knowledge

# FastAPI Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# Security System Configuration
SYSTEM_NAME=HISS Security Guard
MAX_VISITORS_PER_DAY=100
SECURITY_LEVEL=high

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/hiss.log
```

### 5. Initialize Sample Data

Generate sample documents and data for testing:

```bash
python -m src.utils.fake_data
```

This will create:
- Sample security protocol documents
- Sample visitor data
- Knowledge base documents

### 6. Start the System

```bash
python main.py
```

The system will start on `http://localhost:8000`

## Verification

### 1. Check System Health

```bash
curl http://localhost:8000/health
```

### 2. Access API Documentation

Open your browser and go to:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### 3. Test Chat Interface

Open your browser and go to:
- Chat Interface: `http://localhost:8000/chat`

### 4. Register a Test Visitor

```bash
curl -X POST "http://localhost:8000/visitors/register" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Test User",
       "purpose": "Testing the system",
       "company": "Test Corp",
       "contact_email": "<EMAIL>"
     }'
```

## Configuration Options

### Google Gemini API

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add the key to your `.env` file

### ChromaDB Settings

- `CHROMA_DB_PATH`: Directory for vector database storage
- `CHROMA_COLLECTION_NAME`: Name of the document collection

### Security Settings

- `SECURITY_LEVEL`: Default security level (low, medium, high, critical)
- `MAX_VISITORS_PER_DAY`: Maximum visitors allowed per day
- `SYSTEM_NAME`: Name of the security system

### Model Settings

- `MODEL_NAME`: Gemini model to use (default: gemini-1.5-flash)
- `MODEL_TEMPERATURE`: Response creativity (0.0-1.0)
- `MAX_TOKENS`: Maximum response length

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure you're in the virtual environment
   - Run `pip install -e .` again

2. **API Key Issues**
   - Verify your Google Gemini API key is correct
   - Check that the key has proper permissions

3. **ChromaDB Issues**
   - Ensure the data directory is writable
   - Check disk space availability

4. **Port Already in Use**
   - Change the `API_PORT` in `.env`
   - Or stop the process using the port

### Logs

Check the log file for detailed error information:

```bash
tail -f ./logs/hiss.log
```

### Reset System

To reset all data (development only):

```bash
curl -X POST "http://localhost:8000/system/reset"
```

## Development Setup

### Running Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black src/ tests/
isort src/ tests/
```

### Type Checking

```bash
mypy src/
```

## Production Deployment

### Security Considerations

1. **Environment Variables**: Use secure secret management
2. **API Authentication**: Implement proper authentication
3. **HTTPS**: Use SSL/TLS certificates
4. **Rate Limiting**: Add rate limiting middleware
5. **CORS**: Configure CORS appropriately
6. **Logging**: Set up centralized logging

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY . .
RUN pip install -e .

EXPOSE 8000
CMD ["python", "main.py"]
```

Build and run:

```bash
docker build -t hiss-rag .
docker run -p 8000:8000 --env-file .env hiss-rag
```

### Database Considerations

For production, consider:
- Using a persistent database (PostgreSQL, MySQL)
- Implementing proper data backup
- Setting up database migrations
- Using connection pooling

## Support

For issues and questions:
1. Check the logs first
2. Review the API documentation
3. Check the GitHub issues
4. Contact the development team
