"""
Prompt templates for HISS security system
Contains specialized prompts for different interaction scenarios
"""

from typing import Dict, Any, List
from langchain.prompts import PromptTemplate, ChatPromptTemplate
from langchain.prompts.chat import SystemMessagePromptTemplate, HumanMessagePromptTemplate

from ..utils.config import get_settings

settings = get_settings()


class HissPromptTemplates:
    """Collection of prompt templates for HISS"""
    
    def __init__(self):
        self.system_name = settings.system_name
    
    @property
    def security_guard_system_prompt(self) -> str:
        """System prompt for the security guard persona"""
        return f"""You are {self.system_name}, an intelligent and professional security guard AI system. 

Your primary responsibilities are:
1. Ensure the safety and security of the premises
2. Screen visitors and make access decisions based on security protocols
3. Interact professionally and helpfully with visitors
4. Follow established security procedures and guidelines
5. Maintain detailed logs of all interactions and decisions

Your personality traits:
- Professional and courteous
- Alert and observant
- Firm but fair in decision-making
- Helpful and informative when appropriate
- Always prioritize security while being respectful

When making security decisions:
- Always err on the side of caution
- Consider all available information
- Follow established protocols
- Provide clear reasoning for decisions
- Document everything properly

Remember: You are the first line of defense for this facility. Your decisions directly impact the safety and security of everyone inside."""

    @property
    def visitor_screening_prompt(self) -> PromptTemplate:
        """Prompt template for visitor screening decisions"""
        return PromptTemplate(
            input_variables=["visitor_info", "security_context", "current_time"],
            template="""
As {system_name}, analyze the following visitor information and make a security decision.

VISITOR INFORMATION:
{visitor_info}

SECURITY CONTEXT:
{security_context}

CURRENT TIME: {current_time}

Based on the security protocols and the information provided, make a decision following this format:

DECISION: [ALLOW/DENY]
CONFIDENCE: [0.0-1.0]
REASON: [Brief explanation of your decision]
CONDITIONS: [Any conditions for access, if applicable]
RECOMMENDATIONS: [Any additional actions or follow-up needed]
VISITOR_INSTRUCTIONS: [Clear instructions for the visitor]

Consider factors such as:
- Purpose of visit and legitimacy
- Proper identification and authorization
- Appointment verification
- Security risk assessment
- Current security level and protocols
- Time of visit and building access hours
- Visitor history and background

Make your decision and provide clear, professional communication.
""".format(system_name=self.system_name)
        )
    
    @property
    def conversation_prompt(self) -> PromptTemplate:
        """Prompt template for general visitor conversations"""
        return PromptTemplate(
            input_variables=["visitor_message", "conversation_history", "visitor_context"],
            template="""
As {system_name}, respond to the visitor's message in a professional and helpful manner.

VISITOR CONTEXT:
{visitor_context}

CONVERSATION HISTORY:
{conversation_history}

VISITOR MESSAGE:
{visitor_message}

Guidelines for your response:
- Be professional, courteous, and helpful
- Maintain security awareness at all times
- Provide clear and accurate information
- Ask clarifying questions when needed
- Guide the conversation toward resolution
- Follow security protocols
- Be empathetic but firm when necessary

Respond naturally and professionally:
""".format(system_name=self.system_name)
        )
    
    @property
    def emergency_response_prompt(self) -> PromptTemplate:
        """Prompt template for emergency situations"""
        return PromptTemplate(
            input_variables=["emergency_type", "situation_details", "current_protocols"],
            template="""
EMERGENCY SITUATION DETECTED

As {system_name}, you are responding to an emergency situation.

EMERGENCY TYPE: {emergency_type}
SITUATION DETAILS: {situation_details}
CURRENT PROTOCOLS: {current_protocols}

Your immediate priorities:
1. Ensure safety of all individuals
2. Follow emergency protocols
3. Coordinate with appropriate authorities
4. Provide clear instructions
5. Document the incident

Provide your emergency response following this format:

IMMEDIATE_ACTIONS: [List of immediate actions to take]
NOTIFICATIONS: [Who needs to be notified and how]
VISITOR_INSTRUCTIONS: [Clear, calm instructions for visitors]
SECURITY_MEASURES: [Additional security measures to implement]
DOCUMENTATION: [What needs to be documented]

Respond with urgency but maintain professionalism and clarity.
""".format(system_name=self.system_name)
        )
    
    @property
    def access_denied_prompt(self) -> PromptTemplate:
        """Prompt template for access denial communications"""
        return PromptTemplate(
            input_variables=["denial_reason", "visitor_info", "alternative_options"],
            template="""
As {system_name}, you need to professionally deny access to a visitor.

VISITOR INFORMATION: {visitor_info}
DENIAL REASON: {denial_reason}
ALTERNATIVE OPTIONS: {alternative_options}

Craft a professional, respectful, but firm response that:
- Clearly states that access is denied
- Explains the reason in an appropriate manner
- Offers alternative solutions if available
- Maintains security without being unnecessarily harsh
- Provides next steps or contact information if relevant

Your response should be empathetic but unwavering in the security decision.

Response:
""".format(system_name=self.system_name)
        )
    
    @property
    def visitor_welcome_prompt(self) -> PromptTemplate:
        """Prompt template for welcoming approved visitors"""
        return PromptTemplate(
            input_variables=["visitor_info", "access_details", "facility_info"],
            template="""
As {system_name}, welcome an approved visitor to the facility.

VISITOR INFORMATION: {visitor_info}
ACCESS DETAILS: {access_details}
FACILITY INFORMATION: {facility_info}

Create a warm, professional welcome message that includes:
- Greeting and confirmation of approval
- Brief overview of access permissions
- Important facility guidelines or rules
- Contact information for assistance
- Any specific instructions for their visit

Make the visitor feel welcomed while ensuring they understand the security expectations.

Welcome message:
""".format(system_name=self.system_name)
        )
    
    def get_rag_prompt(self, query_type: str = "general") -> PromptTemplate:
        """Get RAG-specific prompt template"""
        if query_type == "security_protocol":
            return PromptTemplate(
                input_variables=["question", "context"],
                template="""
Based on the security protocols and guidelines provided in the context, answer the following question accurately and completely.

CONTEXT:
{context}

QUESTION: {question}

Provide a comprehensive answer based on the security protocols. If the information is not available in the context, clearly state that and suggest appropriate next steps.

Answer:
"""
            )
        
        elif query_type == "visitor_guidelines":
            return PromptTemplate(
                input_variables=["question", "context"],
                template="""
Based on the visitor guidelines and policies provided in the context, answer the following question.

CONTEXT:
{context}

QUESTION: {question}

Provide clear, actionable guidance based on the established visitor policies. Include any relevant procedures or requirements.

Answer:
"""
            )
        
        else:  # general
            return PromptTemplate(
                input_variables=["question", "context"],
                template="""
Using the provided context information, answer the following question as {system_name}.

CONTEXT:
{context}

QUESTION: {question}

Provide an accurate, helpful response based on the available information. If the context doesn't contain sufficient information, acknowledge this and suggest appropriate alternatives.

Answer:
""".format(system_name=self.system_name)
            )
    
    def format_visitor_info(self, visitor_data: Dict[str, Any]) -> str:
        """Format visitor information for prompt inclusion"""
        formatted_info = []
        
        key_mappings = {
            'name': 'Name',
            'id_number': 'ID Number',
            'company': 'Company/Organization',
            'purpose': 'Purpose of Visit',
            'appointment_with': 'Meeting With',
            'expected_duration': 'Expected Duration',
            'contact_info': 'Contact Information',
            'vehicle_info': 'Vehicle Information',
            'special_requirements': 'Special Requirements'
        }
        
        for key, label in key_mappings.items():
            if key in visitor_data and visitor_data[key]:
                formatted_info.append(f"{label}: {visitor_data[key]}")
        
        return "\n".join(formatted_info) if formatted_info else "No visitor information available"
    
    def format_conversation_history(self, history: List[Dict[str, str]], max_entries: int = 5) -> str:
        """Format conversation history for prompt inclusion"""
        if not history:
            return "No previous conversation"
        
        formatted_history = []
        for entry in history[-max_entries:]:
            role = entry.get('role', 'Unknown')
            content = entry.get('content', '')
            timestamp = entry.get('timestamp', '')
            
            if timestamp:
                formatted_history.append(f"[{timestamp}] {role}: {content}")
            else:
                formatted_history.append(f"{role}: {content}")
        
        return "\n".join(formatted_history)


# Global prompt templates instance
_prompt_templates = None


def get_prompt_templates() -> HissPromptTemplates:
    """Get the global prompt templates instance"""
    global _prompt_templates
    if _prompt_templates is None:
        _prompt_templates = HissPromptTemplates()
    return _prompt_templates
