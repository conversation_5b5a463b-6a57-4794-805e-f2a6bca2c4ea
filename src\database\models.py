"""
SQLAlchemy database models for HISS
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import String, Text, DateTime, <PERSON><PERSON><PERSON>, Integer, Float, JSO<PERSON>, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
import uuid

from .config import Base


class User(Base):
    """User table for admin hierarchy"""
    __tablename__ = "users"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    full_name: Mapped[str] = mapped_column(String(100), nullable=False)
    role: Mapped[str] = mapped_column(String(20), nullable=False)  # admin, subadmin, visitor, bot
    is_active: Mapped[bool] = mapped_column(<PERSON>olean, default=True)
    password_hash: Mapped[Optional[str]] = mapped_column(String(255))
    phone_number: Mapped[Optional[str]] = mapped_column(String(20))
    department: Mapped[Optional[str]] = mapped_column(String(50))
    permissions: Mapped[dict] = mapped_column(JSON, default=list)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Relationships
    admin_actions = relationship("AdminAction", back_populates="admin")
    permission_requests_reviewed = relationship("PermissionRequest", back_populates="reviewer")


class Visitor(Base):
    """Visitor information table"""
    __tablename__ = "visitors"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    id_number: Mapped[Optional[str]] = mapped_column(String(50))
    id_type: Mapped[Optional[str]] = mapped_column(String(20))
    company: Mapped[Optional[str]] = mapped_column(String(100))
    purpose: Mapped[str] = mapped_column(String(200), nullable=False)
    appointment_with: Mapped[Optional[str]] = mapped_column(String(100))
    appointment_time: Mapped[Optional[datetime]] = mapped_column(DateTime)
    expected_duration: Mapped[Optional[str]] = mapped_column(String(50))
    contact_phone: Mapped[Optional[str]] = mapped_column(String(20))
    contact_email: Mapped[Optional[str]] = mapped_column(String(100))
    vehicle_info: Mapped[Optional[str]] = mapped_column(String(100))
    special_requirements: Mapped[Optional[str]] = mapped_column(String(200))
    emergency_contact: Mapped[Optional[str]] = mapped_column(String(100))
    status: Mapped[str] = mapped_column(String(20), default="registered")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationships
    conversations = relationship("ConversationMessage", back_populates="visitor")
    security_decisions = relationship("SecurityDecision", back_populates="visitor")
    outdoor_interactions = relationship("OutdoorInteraction", back_populates="visitor")


class ConversationSession(Base):
    """Conversation session tracking"""
    __tablename__ = "conversation_sessions"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    visitor_id: Mapped[Optional[str]] = mapped_column(String(36), ForeignKey("visitors.id"))
    visitor_name: Mapped[Optional[str]] = mapped_column(String(100))
    session_type: Mapped[str] = mapped_column(String(50), default="outdoor_interaction")
    started_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    ended_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    location: Mapped[str] = mapped_column(String(50), default="outdoor")
    total_messages: Mapped[int] = mapped_column(Integer, default=0)
    bot_initiated: Mapped[bool] = mapped_column(Boolean, default=True)
    permission_requested: Mapped[bool] = mapped_column(Boolean, default=False)
    permission_request_id: Mapped[Optional[str]] = mapped_column(String(36))
    final_decision: Mapped[Optional[str]] = mapped_column(String(20))
    session_summary: Mapped[Optional[str]] = mapped_column(Text)
    tags: Mapped[list] = mapped_column(JSON, default=list)
    
    # Relationships
    messages = relationship("ConversationMessage", back_populates="session")


class ConversationMessage(Base):
    """Individual conversation messages"""
    __tablename__ = "conversation_messages"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id: Mapped[Optional[str]] = mapped_column(String(36), ForeignKey("conversation_sessions.id"))
    visitor_id: Mapped[Optional[str]] = mapped_column(String(36), ForeignKey("visitors.id"))
    role: Mapped[str] = mapped_column(String(20), nullable=False)  # visitor, system, security_guard, admin, subadmin
    content: Mapped[str] = mapped_column(Text, nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    message_type: Mapped[Optional[str]] = mapped_column(String(50))
    location: Mapped[str] = mapped_column(String(50), default="outdoor")
    intent_detected: Mapped[Optional[str]] = mapped_column(String(50))
    sentiment: Mapped[Optional[str]] = mapped_column(String(20))
    requires_permission: Mapped[bool] = mapped_column(Boolean, default=False)
    metadata: Mapped[dict] = mapped_column(JSON, default=dict)
    
    # Relationships
    session = relationship("ConversationSession", back_populates="messages")
    visitor = relationship("Visitor", back_populates="conversations")


class OutdoorInteraction(Base):
    """Outdoor visitor interactions"""
    __tablename__ = "outdoor_interactions"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    visitor_id: Mapped[Optional[str]] = mapped_column(String(36), ForeignKey("visitors.id"))
    visitor_name: Mapped[Optional[str]] = mapped_column(String(100))
    visitor_description: Mapped[Optional[str]] = mapped_column(String(200))
    interaction_start: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    interaction_end: Mapped[Optional[datetime]] = mapped_column(DateTime)
    location: Mapped[str] = mapped_column(String(50), default="front_door")
    purpose_stated: Mapped[Optional[str]] = mapped_column(String(200))
    intent_analysis: Mapped[Optional[str]] = mapped_column(String(50))
    bot_assessment: Mapped[Optional[str]] = mapped_column(Text)
    permission_needed: Mapped[bool] = mapped_column(Boolean, default=False)
    permission_request_sent: Mapped[bool] = mapped_column(Boolean, default=False)
    final_outcome: Mapped[Optional[str]] = mapped_column(String(50))
    conversation_summary: Mapped[Optional[str]] = mapped_column(Text)
    total_messages: Mapped[int] = mapped_column(Integer, default=0)
    
    # Relationships
    visitor = relationship("Visitor", back_populates="outdoor_interactions")


class SecurityDecision(Base):
    """Security decisions table"""
    __tablename__ = "security_decisions"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    visitor_id: Mapped[str] = mapped_column(String(36), ForeignKey("visitors.id"), nullable=False)
    decision: Mapped[str] = mapped_column(String(20), nullable=False)  # ALLOW, DENY, PENDING
    confidence: Mapped[float] = mapped_column(Float, nullable=False)
    reason: Mapped[str] = mapped_column(String(500), nullable=False)
    conditions: Mapped[list] = mapped_column(JSON, default=list)
    recommendations: Mapped[list] = mapped_column(JSON, default=list)
    security_level: Mapped[str] = mapped_column(String(20), default="medium")
    decided_by: Mapped[str] = mapped_column(String(100), default="HISS AI System")
    decided_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    valid_until: Mapped[Optional[datetime]] = mapped_column(DateTime)
    additional_data: Mapped[dict] = mapped_column(JSON, default=dict)
    
    # Relationships
    visitor = relationship("Visitor", back_populates="security_decisions")


class PermissionRequest(Base):
    """Permission requests from bot to subadmin"""
    __tablename__ = "permission_requests"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    visitor_id: Mapped[str] = mapped_column(String(36), nullable=False)
    visitor_name: Mapped[str] = mapped_column(String(100), nullable=False)
    visitor_purpose: Mapped[str] = mapped_column(String(200), nullable=False)
    visitor_details: Mapped[dict] = mapped_column(JSON, default=dict)
    bot_conversation_summary: Mapped[str] = mapped_column(Text, nullable=False)
    bot_recommendation: Mapped[str] = mapped_column(String(200), nullable=False)
    bot_confidence: Mapped[float] = mapped_column(Float, nullable=False)
    requested_by: Mapped[str] = mapped_column(String(100), default="HISS Bot")
    requested_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    status: Mapped[str] = mapped_column(String(20), default="pending")  # pending, approved, denied, expired
    assigned_to: Mapped[Optional[str]] = mapped_column(String(36))
    reviewed_by: Mapped[Optional[str]] = mapped_column(String(36), ForeignKey("users.id"))
    reviewed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    decision_reason: Mapped[Optional[str]] = mapped_column(String(500))
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    priority: Mapped[int] = mapped_column(Integer, default=1)
    
    # Relationships
    reviewer = relationship("User", back_populates="permission_requests_reviewed")


class AdminAction(Base):
    """Admin action logging"""
    __tablename__ = "admin_actions"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    admin_id: Mapped[str] = mapped_column(String(36), ForeignKey("users.id"), nullable=False)
    admin_name: Mapped[str] = mapped_column(String(100), nullable=False)
    action_type: Mapped[str] = mapped_column(String(50), nullable=False)
    target_id: Mapped[Optional[str]] = mapped_column(String(36))
    target_type: Mapped[Optional[str]] = mapped_column(String(50))
    description: Mapped[str] = mapped_column(String(500), nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45))
    user_agent: Mapped[Optional[str]] = mapped_column(String(500))
    additional_data: Mapped[dict] = mapped_column(JSON, default=dict)
    
    # Relationships
    admin = relationship("User", back_populates="admin_actions")


class SystemAlert(Base):
    """System alerts table"""
    __tablename__ = "system_alerts"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    alert_type: Mapped[str] = mapped_column(String(50), nullable=False)
    severity: Mapped[str] = mapped_column(String(20), default="medium")
    title: Mapped[str] = mapped_column(String(100), nullable=False)
    message: Mapped[str] = mapped_column(String(500), nullable=False)
    source: Mapped[str] = mapped_column(String(100), default="HISS System")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    acknowledged: Mapped[bool] = mapped_column(Boolean, default=False)
    acknowledged_by: Mapped[Optional[str]] = mapped_column(String(36))
    acknowledged_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    resolved: Mapped[bool] = mapped_column(Boolean, default=False)
    resolved_by: Mapped[Optional[str]] = mapped_column(String(36))
    resolved_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    related_id: Mapped[Optional[str]] = mapped_column(String(36))
    metadata: Mapped[dict] = mapped_column(JSON, default=dict)
