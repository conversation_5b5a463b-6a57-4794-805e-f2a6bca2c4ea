"""
Tests for HISS API endpoints
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import json

from src.api.main import app
from src.models.security import VisitorRegistrationRequest


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def sample_visitor_data():
    """Sample visitor data for testing"""
    return {
        "name": "Test User",
        "purpose": "Testing API",
        "company": "Test Corp",
        "contact_email": "<EMAIL>",
        "contact_phone": "******-0123"
    }


class TestHealthEndpoints:
    """Test health and status endpoints"""
    
    def test_root_endpoint(self, client):
        """Test root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "HISS" in data["message"]
    
    @patch('src.api.main.get_vector_store')
    def test_health_check(self, mock_vector_store, client):
        """Test health check endpoint"""
        # Mock vector store
        mock_store = MagicMock()
        mock_store.get_collection_info.return_value = {"document_count": 10}
        mock_vector_store.return_value = mock_store
        
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "online"


class TestVisitorEndpoints:
    """Test visitor-related endpoints"""
    
    def test_register_visitor(self, client, sample_visitor_data):
        """Test visitor registration"""
        response = client.post("/visitors/register", json=sample_visitor_data)
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "visitor_id" in data["data"]
    
    def test_register_visitor_invalid_data(self, client):
        """Test visitor registration with invalid data"""
        invalid_data = {
            "name": "",  # Invalid: empty name
            "purpose": "Test"
        }
        response = client.post("/visitors/register", json=invalid_data)
        assert response.status_code == 400
    
    def test_list_visitors(self, client, sample_visitor_data):
        """Test listing visitors"""
        # First register a visitor
        client.post("/visitors/register", json=sample_visitor_data)
        
        # Then list visitors
        response = client.get("/visitors")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
    
    def test_get_visitor_not_found(self, client):
        """Test getting non-existent visitor"""
        response = client.get("/visitors/nonexistent-id")
        assert response.status_code == 404


class TestChatEndpoints:
    """Test chat-related endpoints"""
    
    def test_chat_interface_page(self, client):
        """Test chat interface HTML page"""
        response = client.get("/chat")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        assert "HISS Security Assistant" in response.text
    
    @patch('src.api.main.get_llm')
    def test_chat_endpoint(self, mock_llm, client):
        """Test chat endpoint"""
        # Mock LLM response
        mock_llm_instance = MagicMock()
        mock_llm_instance.generate_conversation_response.return_value = "Hello! How can I help you?"
        mock_llm.return_value = mock_llm_instance
        
        chat_data = {
            "message": "Hello, I need help",
            "visitor_id": "test-visitor"
        }
        
        response = client.post("/chat", json=chat_data)
        assert response.status_code == 200
        data = response.json()
        assert "response" in data
        assert "message_id" in data


class TestSystemEndpoints:
    """Test system management endpoints"""
    
    def test_reset_system(self, client):
        """Test system reset"""
        response = client.post("/system/reset")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    @patch('src.api.main.get_vector_store')
    def test_knowledge_info(self, mock_vector_store, client):
        """Test knowledge base info endpoint"""
        # Mock vector store
        mock_store = MagicMock()
        mock_store.get_collection_info.return_value = {
            "name": "test_collection",
            "document_count": 5
        }
        mock_vector_store.return_value = mock_store
        
        response = client.get("/knowledge/info")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "document_count" in data["data"]


if __name__ == "__main__":
    pytest.main([__file__])
