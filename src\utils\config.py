"""
Configuration management for HISS
Handles environment variables and application settings
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # Google Gemini API Configuration
    google_api_key: str = Field(..., env="GOOGLE_API_KEY")

    # Database Configuration
    database_url: str = Field(..., env="DATABASE_URL")

    # ChromaDB Configuration
    chroma_db_path: str = Field(default="./data/chroma_db", env="CHROMA_DB_PATH")
    chroma_collection_name: str = Field(default="hiss_security_knowledge", env="CHROMA_COLLECTION_NAME")
    
    # FastAPI Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_reload: bool = Field(default=True, env="API_RELOAD")
    
    # Security System Configuration
    system_name: str = Field(default="HISS Security Guard", env="SYSTEM_NAME")
    max_visitors_per_day: int = Field(default=100, env="MAX_VISITORS_PER_DAY")
    security_level: str = Field(default="high", env="SECURITY_LEVEL")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="./logs/hiss.log", env="LOG_FILE")
    
    # Model Configuration
    model_name: str = Field(default="gemini-1.5-flash", env="MODEL_NAME")
    model_temperature: float = Field(default=0.7, env="MODEL_TEMPERATURE")
    max_tokens: int = Field(default=1000, env="MAX_TOKENS")
    
    # Vector Store Configuration
    embedding_model: str = Field(default="models/embedding-001", env="EMBEDDING_MODEL")
    chunk_size: int = Field(default=1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="CHUNK_OVERLAP")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings


def ensure_directories():
    """Ensure required directories exist"""
    directories = [
        os.path.dirname(settings.chroma_db_path),
        os.path.dirname(settings.log_file),
        "./data/documents",
        "./data/fake_data"
    ]
    
    for directory in directories:
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)


# Initialize directories on import
ensure_directories()
