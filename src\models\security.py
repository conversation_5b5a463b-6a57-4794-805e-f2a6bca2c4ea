"""
Pydantic models for HISS security system
Defines data structures for visitors, decisions, logs, and responses
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any, Literal
from enum import Enum
from pydantic import BaseModel, Field, validator
import uuid


class SecurityLevel(str, Enum):
    """Security levels for the system"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AccessDecision(str, Enum):
    """Access decision types"""
    ALLOW = "ALLOW"
    DENY = "DENY"
    PENDING = "PENDING"


class VisitorStatus(str, Enum):
    """Visitor status types"""
    REGISTERED = "registered"
    CHECKED_IN = "checked_in"
    INSIDE = "inside"
    CHECKED_OUT = "checked_out"
    DENIED = "denied"


class EmergencyType(str, Enum):
    """Emergency types"""
    FIRE = "fire"
    MEDICAL = "medical"
    SECURITY_BREACH = "security_breach"
    EVACUATION = "evacuation"
    LOCKDOWN = "lockdown"
    OTHER = "other"


class VisitorInfo(BaseModel):
    """Visitor information model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = Field(..., min_length=1, max_length=100)
    id_number: Optional[str] = Field(None, max_length=50)
    id_type: Optional[str] = Field(None, max_length=20)  # passport, license, etc.
    company: Optional[str] = Field(None, max_length=100)
    purpose: str = Field(..., min_length=1, max_length=200)
    appointment_with: Optional[str] = Field(None, max_length=100)
    appointment_time: Optional[datetime] = None
    expected_duration: Optional[str] = Field(None, max_length=50)
    contact_phone: Optional[str] = Field(None, max_length=20)
    contact_email: Optional[str] = Field(None, max_length=100)
    vehicle_info: Optional[str] = Field(None, max_length=100)
    special_requirements: Optional[str] = Field(None, max_length=200)
    emergency_contact: Optional[str] = Field(None, max_length=100)
    status: VisitorStatus = Field(default=VisitorStatus.REGISTERED)
    created_at: datetime = Field(default_factory=datetime.now)
    
    @validator('contact_email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v
    
    class Config:
        use_enum_values = True


class SecurityDecision(BaseModel):
    """Security decision model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    visitor_id: str
    decision: AccessDecision
    confidence: float = Field(..., ge=0.0, le=1.0)
    reason: str = Field(..., min_length=1, max_length=500)
    conditions: Optional[List[str]] = Field(default_factory=list)
    recommendations: Optional[List[str]] = Field(default_factory=list)
    security_level: SecurityLevel = Field(default=SecurityLevel.MEDIUM)
    decided_by: str = Field(default="HISS AI System")
    decided_at: datetime = Field(default_factory=datetime.now)
    valid_until: Optional[datetime] = None
    additional_data: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True


class AccessLog(BaseModel):
    """Access log entry model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    visitor_id: str
    action: Literal["check_in", "check_out", "denied_entry", "emergency_exit"]
    timestamp: datetime = Field(default_factory=datetime.now)
    location: Optional[str] = Field(None, max_length=100)
    security_decision_id: Optional[str] = None
    notes: Optional[str] = Field(None, max_length=500)
    system_user: str = Field(default="HISS System")
    
    class Config:
        use_enum_values = True


class ConversationMessage(BaseModel):
    """Conversation message model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    visitor_id: Optional[str] = None
    role: Literal["visitor", "system", "security_guard"]
    content: str = Field(..., min_length=1, max_length=2000)
    timestamp: datetime = Field(default_factory=datetime.now)
    message_type: Optional[str] = Field(None, max_length=50)  # greeting, question, response, etc.
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True


class SecurityAlert(BaseModel):
    """Security alert model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    alert_type: str = Field(..., max_length=50)
    severity: SecurityLevel
    title: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=1, max_length=500)
    location: Optional[str] = Field(None, max_length=100)
    visitor_id: Optional[str] = None
    triggered_by: str = Field(default="HISS System")
    triggered_at: datetime = Field(default_factory=datetime.now)
    resolved: bool = Field(default=False)
    resolved_at: Optional[datetime] = None
    resolved_by: Optional[str] = None
    actions_taken: Optional[List[str]] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True


class EmergencyEvent(BaseModel):
    """Emergency event model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    emergency_type: EmergencyType
    title: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=1, max_length=1000)
    location: Optional[str] = Field(None, max_length=100)
    severity: SecurityLevel = Field(default=SecurityLevel.HIGH)
    reported_by: str = Field(..., max_length=100)
    reported_at: datetime = Field(default_factory=datetime.now)
    status: Literal["active", "resolved", "investigating"] = Field(default="active")
    affected_areas: Optional[List[str]] = Field(default_factory=list)
    evacuation_required: bool = Field(default=False)
    authorities_notified: bool = Field(default=False)
    response_actions: Optional[List[str]] = Field(default_factory=list)
    resolved_at: Optional[datetime] = None
    
    class Config:
        use_enum_values = True


class SystemResponse(BaseModel):
    """System response model"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error_code: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class VisitorRegistrationRequest(BaseModel):
    """Request model for visitor registration"""
    name: str = Field(..., min_length=1, max_length=100)
    id_number: Optional[str] = Field(None, max_length=50)
    id_type: Optional[str] = Field(None, max_length=20)
    company: Optional[str] = Field(None, max_length=100)
    purpose: str = Field(..., min_length=1, max_length=200)
    appointment_with: Optional[str] = Field(None, max_length=100)
    appointment_time: Optional[datetime] = None
    expected_duration: Optional[str] = Field(None, max_length=50)
    contact_phone: Optional[str] = Field(None, max_length=20)
    contact_email: Optional[str] = Field(None, max_length=100)
    vehicle_info: Optional[str] = Field(None, max_length=100)
    special_requirements: Optional[str] = Field(None, max_length=200)
    emergency_contact: Optional[str] = Field(None, max_length=100)


class ChatRequest(BaseModel):
    """Chat request model"""
    visitor_id: Optional[str] = None
    message: str = Field(..., min_length=1, max_length=2000)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)


class ChatResponse(BaseModel):
    """Chat response model"""
    message_id: str
    response: str
    timestamp: datetime = Field(default_factory=datetime.now)
    suggestions: Optional[List[str]] = Field(default_factory=list)
    requires_action: bool = Field(default=False)
    action_type: Optional[str] = None


class SystemStatus(BaseModel):
    """System status model"""
    status: Literal["online", "offline", "maintenance", "emergency"]
    security_level: SecurityLevel
    active_visitors: int = Field(default=0)
    pending_decisions: int = Field(default=0)
    active_alerts: int = Field(default=0)
    last_updated: datetime = Field(default_factory=datetime.now)
    system_version: str = Field(default="1.0.0")
    uptime: Optional[str] = None
