"""
Outdoor Bot Agent for HISS
Handles interactions with visitors outside the home/building
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import re

from ..models.security import OutdoorInteraction, ConversationMessage
from ..models.admin import ConversationSession
from ..utils.llm import get_llm
from ..prompts.templates import get_prompt_templates
from ..utils.logger import get_logger, log_interaction
from ..api.subadmin_panel import create_permission_request

logger = get_logger(__name__)


class OutdoorBotAgent:
    """Outdoor interaction bot for HISS"""
    
    def __init__(self):
        self.llm = get_llm()
        self.prompts = get_prompt_templates()
        self.active_interactions: Dict[str, OutdoorInteraction] = {}
        self.conversation_sessions: Dict[str, ConversationSession] = {}
        
        # Intent patterns for visitor purpose detection
        self.intent_patterns = {
            "home_visit": [
                r"visit.*home", r"see.*family", r"meet.*friend", r"visiting.*someone",
                r"come.*inside", r"enter.*house", r"go.*in"
            ],
            "delivery": [
                r"deliver", r"package", r"mail", r"courier", r"drop.*off",
                r"amazon", r"fedex", r"ups", r"dhl"
            ],
            "service": [
                r"repair", r"fix", r"maintenance", r"plumber", r"electrician",
                r"internet", r"cable", r"service.*call"
            ],
            "emergency": [
                r"emergency", r"urgent", r"help", r"police", r"fire", r"medical",
                r"ambulance", r"911"
            ],
            "sales": [
                r"sell", r"offer", r"product", r"service", r"business",
                r"insurance", r"solar", r"security.*system"
            ]
        }
    
    def detect_intent(self, message: str) -> str:
        """Detect visitor intent from message"""
        message_lower = message.lower()
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message_lower):
                    return intent
        
        return "general"
    
    def analyze_visitor_sentiment(self, message: str) -> str:
        """Analyze visitor sentiment"""
        message_lower = message.lower()
        
        positive_words = ["please", "thank", "appreciate", "kind", "help", "polite"]
        negative_words = ["angry", "upset", "frustrated", "demand", "insist", "rude"]
        urgent_words = ["urgent", "emergency", "immediate", "asap", "quickly"]
        
        positive_count = sum(1 for word in positive_words if word in message_lower)
        negative_count = sum(1 for word in negative_words if word in message_lower)
        urgent_count = sum(1 for word in urgent_words if word in message_lower)
        
        if urgent_count > 0:
            return "urgent"
        elif negative_count > positive_count:
            return "negative"
        elif positive_count > 0:
            return "positive"
        else:
            return "neutral"
    
    def start_outdoor_interaction(self, visitor_description: str = None) -> str:
        """Start a new outdoor interaction"""
        interaction = OutdoorInteraction(
            visitor_description=visitor_description,
            location="front_door"
        )
        
        session = ConversationSession(
            session_type="outdoor_interaction",
            location="outdoor"
        )
        
        self.active_interactions[interaction.id] = interaction
        self.conversation_sessions[interaction.id] = session
        
        # Generate greeting
        greeting = self.generate_outdoor_greeting()
        
        # Create initial message
        message = ConversationMessage(
            session_id=interaction.id,
            role="security_guard",
            content=greeting,
            location="outdoor",
            message_type="greeting"
        )
        
        log_interaction(
            interaction_type="outdoor_greeting",
            message=greeting,
            additional_data={"interaction_id": interaction.id}
        )
        
        return interaction.id
    
    def generate_outdoor_greeting(self) -> str:
        """Generate friendly outdoor greeting"""
        greetings = [
            "Hello! I'm the AI security assistant for this home. How can I help you today?",
            "Good day! I'm here to assist visitors. What brings you here today?",
            "Hi there! I'm the digital security guard. How may I assist you?",
            "Welcome! I'm the AI assistant for this residence. What can I do for you?",
            "Hello! I'm the smart security system. How can I help you today?"
        ]
        
        import random
        return random.choice(greetings)
    
    def process_outdoor_message(
        self, 
        interaction_id: str, 
        visitor_message: str,
        visitor_name: str = None
    ) -> Tuple[str, bool]:
        """
        Process visitor message and return response and whether permission is needed
        Returns: (response_message, needs_permission)
        """
        
        if interaction_id not in self.active_interactions:
            raise ValueError("Interaction not found")
        
        interaction = self.active_interactions[interaction_id]
        session = self.conversation_sessions[interaction_id]
        
        # Update interaction details
        if visitor_name and not interaction.visitor_name:
            interaction.visitor_name = visitor_name
            session.visitor_name = visitor_name
        
        # Detect intent and sentiment
        intent = self.detect_intent(visitor_message)
        sentiment = self.analyze_visitor_sentiment(visitor_message)
        
        # Create visitor message
        visitor_msg = ConversationMessage(
            session_id=interaction_id,
            role="visitor",
            content=visitor_message,
            location="outdoor",
            intent_detected=intent,
            sentiment=sentiment,
            message_type="question"
        )
        
        # Update session stats
        session.total_messages += 1
        interaction.total_messages += 1
        
        # Generate contextual response
        response = self.generate_outdoor_response(
            interaction, visitor_message, intent, sentiment
        )
        
        # Create bot response message
        bot_msg = ConversationMessage(
            session_id=interaction_id,
            role="security_guard",
            content=response,
            location="outdoor",
            message_type="response"
        )
        
        # Check if permission is needed
        needs_permission = self.check_permission_needed(intent, visitor_message, interaction)
        
        if needs_permission and not interaction.permission_request_sent:
            # Update interaction
            interaction.permission_needed = True
            interaction.purpose_stated = visitor_message
            interaction.intent_analysis = intent
            
            # Generate summary for subadmin
            summary = self.generate_conversation_summary(interaction)
            recommendation = self.generate_bot_recommendation(interaction, intent, sentiment)
            confidence = self.calculate_confidence(interaction, intent, sentiment)
            
            # Create permission request
            permission_request = create_permission_request(
                visitor_id=interaction.id,
                visitor_name=interaction.visitor_name or "Unknown Visitor",
                visitor_purpose=interaction.purpose_stated,
                bot_summary=summary,
                bot_recommendation=recommendation,
                bot_confidence=confidence,
                visitor_details={
                    "intent": intent,
                    "sentiment": sentiment,
                    "location": interaction.location,
                    "interaction_start": interaction.interaction_start.isoformat(),
                    "total_messages": interaction.total_messages
                }
            )
            
            interaction.permission_request_sent = True
            session.permission_requested = True
            session.permission_request_id = permission_request.id
            
            # Add permission request info to response
            response += "\n\nI've sent your request to our security team for approval. Please wait a moment while they review it."
        
        # Log interaction
        log_interaction(
            interaction_type="outdoor_conversation",
            visitor_id=interaction.id,
            message=visitor_message,
            response=response,
            additional_data={
                "intent": intent,
                "sentiment": sentiment,
                "needs_permission": needs_permission
            }
        )
        
        return response, needs_permission
    
    def generate_outdoor_response(
        self, 
        interaction: OutdoorInteraction, 
        visitor_message: str, 
        intent: str, 
        sentiment: str
    ) -> str:
        """Generate contextual response for outdoor interaction"""
        
        # Build context for LLM
        context_parts = [
            f"Visitor location: {interaction.location}",
            f"Interaction duration: {(datetime.now() - interaction.interaction_start).total_seconds():.0f} seconds",
            f"Detected intent: {intent}",
            f"Visitor sentiment: {sentiment}",
            f"Total messages: {interaction.total_messages}"
        ]
        
        if interaction.visitor_name:
            context_parts.append(f"Visitor name: {interaction.visitor_name}")
        
        context = "\n".join(context_parts)
        
        # Create specialized prompt for outdoor interaction
        outdoor_prompt = f"""
        You are an AI security assistant stationed outside a home. You are interacting with a visitor at the front door.
        
        Your role:
        - Be friendly, professional, and helpful
        - Gather information about the visitor's purpose
        - Determine if they need access to the home
        - Provide helpful information when appropriate
        - Maintain security while being welcoming
        
        Current context:
        {context}
        
        Visitor's message: {visitor_message}
        
        Guidelines:
        - If they want to enter the home, explain that you need to get permission
        - For deliveries, ask about package details and recipient
        - For emergencies, prioritize immediate assistance
        - For sales/solicitation, politely decline but be respectful
        - Ask clarifying questions to understand their needs
        - Keep responses conversational and human-like
        
        Respond naturally and helpfully:
        """
        
        try:
            response = self.llm.generate_response(
                prompt=outdoor_prompt,
                system_message="You are a friendly but security-conscious AI assistant helping visitors outside a home."
            )
            return response
        except Exception as e:
            logger.error(f"Failed to generate outdoor response: {e}")
            return "I apologize, but I'm having technical difficulties. Please wait a moment or contact the homeowner directly if this is urgent."
    
    def check_permission_needed(self, intent: str, message: str, interaction: OutdoorInteraction) -> bool:
        """Check if visitor needs permission to enter"""
        
        # Always need permission for home visits
        if intent == "home_visit":
            return True
        
        # Check for explicit entry requests
        entry_keywords = ["come in", "enter", "go inside", "let me in", "open door", "access"]
        message_lower = message.lower()
        
        for keyword in entry_keywords:
            if keyword in message_lower:
                return True
        
        # Emergency situations might need immediate access
        if intent == "emergency":
            return True
        
        return False
    
    def generate_conversation_summary(self, interaction: OutdoorInteraction) -> str:
        """Generate summary of conversation for subadmin"""
        
        summary_parts = [
            f"Visitor at {interaction.location} since {interaction.interaction_start.strftime('%H:%M')}",
            f"Total conversation messages: {interaction.total_messages}",
        ]
        
        if interaction.visitor_name:
            summary_parts.append(f"Visitor identified as: {interaction.visitor_name}")
        
        if interaction.purpose_stated:
            summary_parts.append(f"Stated purpose: {interaction.purpose_stated}")
        
        if interaction.intent_analysis:
            summary_parts.append(f"Detected intent: {interaction.intent_analysis}")
        
        return ". ".join(summary_parts)
    
    def generate_bot_recommendation(self, interaction: OutdoorInteraction, intent: str, sentiment: str) -> str:
        """Generate bot recommendation for subadmin"""
        
        if intent == "emergency":
            return "RECOMMEND IMMEDIATE ACCESS - Emergency situation detected"
        elif intent == "home_visit" and sentiment in ["positive", "neutral"]:
            return "RECOMMEND APPROVAL - Legitimate home visit request"
        elif intent == "delivery":
            return "RECOMMEND CONDITIONAL APPROVAL - Package delivery, verify recipient"
        elif intent == "service":
            return "RECOMMEND VERIFICATION - Service call, confirm appointment"
        elif intent == "sales":
            return "RECOMMEND DENIAL - Unsolicited sales visit"
        else:
            return "RECOMMEND MANUAL REVIEW - Unclear intent, requires human judgment"
    
    def calculate_confidence(self, interaction: OutdoorInteraction, intent: str, sentiment: str) -> float:
        """Calculate bot confidence in recommendation"""
        
        base_confidence = 0.7
        
        # Adjust based on intent clarity
        if intent in ["emergency", "delivery", "sales"]:
            base_confidence += 0.2
        elif intent == "general":
            base_confidence -= 0.2
        
        # Adjust based on sentiment
        if sentiment == "positive":
            base_confidence += 0.1
        elif sentiment == "negative":
            base_confidence -= 0.1
        elif sentiment == "urgent":
            base_confidence += 0.15
        
        # Adjust based on interaction length
        if interaction.total_messages >= 3:
            base_confidence += 0.1
        
        # Ensure confidence is within bounds
        return max(0.0, min(1.0, base_confidence))
    
    def end_interaction(self, interaction_id: str, outcome: str):
        """End an outdoor interaction"""
        
        if interaction_id in self.active_interactions:
            interaction = self.active_interactions[interaction_id]
            session = self.conversation_sessions[interaction_id]
            
            interaction.interaction_end = datetime.now()
            interaction.final_outcome = outcome
            
            session.ended_at = datetime.now()
            session.final_decision = outcome
            
            # Generate final summary
            interaction.conversation_summary = self.generate_conversation_summary(interaction)
            session.session_summary = interaction.conversation_summary
            
            logger.info(f"Outdoor interaction {interaction_id} ended with outcome: {outcome}")
    
    def get_interaction_status(self, interaction_id: str) -> Dict[str, Any]:
        """Get current status of an interaction"""
        
        if interaction_id not in self.active_interactions:
            return {"error": "Interaction not found"}
        
        interaction = self.active_interactions[interaction_id]
        session = self.conversation_sessions[interaction_id]
        
        return {
            "interaction_id": interaction_id,
            "visitor_name": interaction.visitor_name,
            "location": interaction.location,
            "started_at": interaction.interaction_start,
            "total_messages": interaction.total_messages,
            "permission_needed": interaction.permission_needed,
            "permission_sent": interaction.permission_request_sent,
            "intent": interaction.intent_analysis,
            "status": "active" if not interaction.interaction_end else "ended",
            "outcome": interaction.final_outcome
        }


# Global outdoor bot instance
_outdoor_bot = None


def get_outdoor_bot() -> OutdoorBotAgent:
    """Get the global outdoor bot instance"""
    global _outdoor_bot
    if _outdoor_bot is None:
        _outdoor_bot = OutdoorBotAgent()
    return _outdoor_bot
