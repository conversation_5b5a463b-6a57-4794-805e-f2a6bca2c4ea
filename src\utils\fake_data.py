"""
Fake data generator for HISS
Creates realistic test data for visitors, security protocols, and system knowledge
"""

import random
from datetime import datetime, timedelta
from typing import List, Dict, Any
from faker import Faker
import json
import os
from pathlib import Path

from ..models.security import VisitorInfo, SecurityDecision, AccessDecision, SecurityLevel
from ..utils.config import get_settings

settings = get_settings()
fake = Faker()


class HissFakeDataGenerator:
    """Generate fake data for HISS system testing"""
    
    def __init__(self):
        self.companies = [
            "TechCorp Solutions", "Global Industries", "Innovation Labs",
            "SecureNet Systems", "DataFlow Analytics", "CloudFirst Technologies",
            "NextGen Enterprises", "Digital Dynamics", "SmartBuild Construction",
            "HealthTech Medical", "EcoGreen Energy", "FinanceFlow Banking"
        ]
        
        self.visit_purposes = [
            "Business meeting", "Technical consultation", "Project review",
            "Contract negotiation", "System maintenance", "Training session",
            "Audit and compliance", "Vendor presentation", "Partnership discussion",
            "Emergency repair", "Delivery coordination", "Interview process"
        ]
        
        self.departments = [
            "Engineering", "Sales", "Marketing", "Finance", "HR",
            "Operations", "IT", "Security", "Legal", "Research"
        ]
        
        self.id_types = ["passport", "driver_license", "national_id", "employee_badge"]
    
    def generate_visitor(self) -> VisitorInfo:
        """Generate a fake visitor"""
        name = fake.name()
        company = random.choice(self.companies) if random.random() > 0.3 else None
        purpose = random.choice(self.visit_purposes)
        
        # Generate appointment time (50% chance of having one)
        appointment_time = None
        if random.random() > 0.5:
            appointment_time = fake.date_time_between(
                start_date=datetime.now(),
                end_date=datetime.now() + timedelta(days=7)
            )
        
        visitor = VisitorInfo(
            name=name,
            id_number=fake.random_number(digits=8, fix_len=True),
            id_type=random.choice(self.id_types),
            company=company,
            purpose=purpose,
            appointment_with=fake.name() if random.random() > 0.4 else None,
            appointment_time=appointment_time,
            expected_duration=random.choice(["30 minutes", "1 hour", "2 hours", "Half day", "Full day"]),
            contact_phone=fake.phone_number(),
            contact_email=fake.email(),
            vehicle_info=f"{fake.color_name()} {fake.random_element(['Toyota', 'Honda', 'Ford', 'BMW', 'Mercedes'])} - {fake.license_plate()}" if random.random() > 0.6 else None,
            special_requirements=random.choice([
                None, "Wheelchair access", "Interpreter needed", 
                "Laptop required", "Parking space", "After hours access"
            ]),
            emergency_contact=fake.name() + " - " + fake.phone_number()
        )
        
        return visitor
    
    def generate_visitors(self, count: int = 10) -> List[VisitorInfo]:
        """Generate multiple fake visitors"""
        return [self.generate_visitor() for _ in range(count)]
    
    def generate_security_protocols(self) -> List[Dict[str, str]]:
        """Generate fake security protocol documents"""
        protocols = [
            {
                "title": "Visitor Access Control Policy",
                "content": """
                VISITOR ACCESS CONTROL POLICY
                
                1. IDENTIFICATION REQUIREMENTS
                All visitors must present valid government-issued photo identification upon arrival.
                Acceptable forms of ID include:
                - Passport
                - Driver's License
                - National ID Card
                - Military ID
                
                2. REGISTRATION PROCESS
                - All visitors must register at the main security desk
                - Provide purpose of visit and person/department being visited
                - Complete visitor log with contact information
                - Receive visitor badge and access instructions
                
                3. ESCORT REQUIREMENTS
                - Unescorted access is limited to public areas only
                - Visitors to restricted areas must be escorted at all times
                - Host employees are responsible for their visitors
                
                4. RESTRICTED AREAS
                The following areas require special authorization:
                - Server rooms and data centers
                - Executive offices
                - Research and development labs
                - Financial departments
                
                5. BUSINESS HOURS
                Standard visitor access: Monday-Friday, 8:00 AM - 6:00 PM
                After-hours access requires prior approval and security escort.
                """
            },
            {
                "title": "Emergency Procedures",
                "content": """
                EMERGENCY PROCEDURES
                
                1. FIRE EMERGENCY
                - Immediately evacuate the building via nearest exit
                - Do not use elevators
                - Proceed to designated assembly point
                - Report to floor wardens for headcount
                
                2. MEDICAL EMERGENCY
                - Call 911 immediately
                - Notify security at extension 2911
                - Provide first aid if trained
                - Clear area for emergency responders
                
                3. SECURITY BREACH
                - Report suspicious activity immediately
                - Do not confront unauthorized individuals
                - Secure sensitive materials
                - Follow lockdown procedures if announced
                
                4. EVACUATION PROCEDURES
                - Follow instructions from security personnel
                - Assist visitors and disabled individuals
                - Do not re-enter building until all-clear given
                - Account for all personnel at assembly points
                """
            },
            {
                "title": "Visitor Guidelines and Expectations",
                "content": """
                VISITOR GUIDELINES
                
                1. PROFESSIONAL CONDUCT
                - Maintain professional behavior at all times
                - Respect company property and personnel
                - Follow all posted signs and instructions
                - Report any incidents to security
                
                2. TECHNOLOGY POLICY
                - Personal devices subject to security screening
                - Photography/recording prohibited without permission
                - Guest WiFi available for business use
                - No unauthorized software installation
                
                3. CONFIDENTIALITY
                - Respect proprietary and confidential information
                - Sign NDA if required for your visit
                - Do not discuss sensitive matters in public areas
                - Return all materials before departure
                
                4. HEALTH AND SAFETY
                - Report any health concerns immediately
                - Follow safety protocols in all areas
                - Wear required safety equipment when provided
                - No smoking anywhere on premises
                
                5. DEPARTURE PROCEDURES
                - Return visitor badge to security
                - Sign out in visitor log
                - Ensure all materials are returned
                - Exit through designated areas only
                """
            }
        ]
        
        return protocols
    
    def create_sample_documents(self):
        """Create sample document files for the knowledge base"""
        try:
            # Create directories
            docs_dir = Path("./data/documents")
            protocols_dir = docs_dir / "protocols"
            guidelines_dir = docs_dir / "guidelines"
            emergency_dir = docs_dir / "emergency"
            
            for directory in [protocols_dir, guidelines_dir, emergency_dir]:
                directory.mkdir(parents=True, exist_ok=True)
            
            # Generate security protocols
            protocols = self.generate_security_protocols()
            
            # Save protocol documents
            with open(protocols_dir / "visitor_access_policy.txt", "w") as f:
                f.write(protocols[0]["content"])
            
            with open(emergency_dir / "emergency_procedures.txt", "w") as f:
                f.write(protocols[1]["content"])
            
            with open(guidelines_dir / "visitor_guidelines.txt", "w") as f:
                f.write(protocols[2]["content"])
            
            # Create additional sample documents
            self._create_additional_documents(protocols_dir, guidelines_dir, emergency_dir)
            
            print(f"Sample documents created in {docs_dir}")
            
        except Exception as e:
            print(f"Failed to create sample documents: {e}")
    
    def _create_additional_documents(self, protocols_dir, guidelines_dir, emergency_dir):
        """Create additional sample documents"""
        
        # Security protocols
        with open(protocols_dir / "id_verification.txt", "w") as f:
            f.write("""
            ID VERIFICATION PROCEDURES
            
            1. Check photo matches person
            2. Verify ID is not expired
            3. Look for signs of tampering
            4. Cross-reference with appointment list
            5. Record ID details in visitor log
            6. Return ID to visitor after verification
            """)
        
        # Visitor guidelines
        with open(guidelines_dir / "parking_policy.txt", "w") as f:
            f.write("""
            VISITOR PARKING POLICY
            
            1. Visitor parking available in designated areas only
            2. Display visitor parking permit on dashboard
            3. Maximum stay: 8 hours without extension
            4. No overnight parking without special permission
            5. Vehicles may be subject to security inspection
            6. Report any parking issues to security
            """)
        
        # Emergency procedures
        with open(emergency_dir / "lockdown_procedures.txt", "w") as f:
            f.write("""
            LOCKDOWN PROCEDURES
            
            1. Remain calm and follow instructions
            2. Move away from windows and doors
            3. Turn off lights and remain quiet
            4. Do not use phones unless emergency
            5. Wait for all-clear from security
            6. Account for all visitors in your area
            """)
    
    def generate_sample_data_file(self, filename: str = "sample_visitors.json"):
        """Generate and save sample visitor data to file"""
        try:
            visitors = self.generate_visitors(20)
            visitor_data = [visitor.dict() for visitor in visitors]
            
            data_dir = Path("./data/fake_data")
            data_dir.mkdir(parents=True, exist_ok=True)
            
            with open(data_dir / filename, "w") as f:
                json.dump(visitor_data, f, indent=2, default=str)
            
            print(f"Sample visitor data saved to {data_dir / filename}")
            
        except Exception as e:
            print(f"Failed to generate sample data file: {e}")
    
    def load_sample_visitors(self, filename: str = "sample_visitors.json") -> List[VisitorInfo]:
        """Load sample visitors from file"""
        try:
            data_file = Path("./data/fake_data") / filename
            
            if not data_file.exists():
                print(f"Sample data file not found: {data_file}")
                return []
            
            with open(data_file, "r") as f:
                visitor_data = json.load(f)
            
            visitors = [VisitorInfo(**data) for data in visitor_data]
            return visitors
            
        except Exception as e:
            print(f"Failed to load sample visitors: {e}")
            return []


def initialize_sample_data():
    """Initialize all sample data for the system"""
    generator = HissFakeDataGenerator()
    
    print("Generating sample data for HISS...")
    
    # Create sample documents
    generator.create_sample_documents()
    
    # Generate sample visitor data
    generator.generate_sample_data_file()
    
    print("Sample data generation completed!")


# Global fake data generator instance
_fake_data_generator = None


def get_fake_data_generator() -> HissFakeDataGenerator:
    """Get the global fake data generator instance"""
    global _fake_data_generator
    if _fake_data_generator is None:
        _fake_data_generator = HissFakeDataGenerator()
    return _fake_data_generator


if __name__ == "__main__":
    initialize_sample_data()
