"""
Outdoor Chat Interface for HISS
Specialized interface for outdoor visitor interactions
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from typing import Dict, Any
import json
import asyncio

from ..agents.outdoor_bot import get_outdoor_bot
from ..utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/outdoor", tags=["outdoor"])


class OutdoorConnectionManager:
    """Manages outdoor WebSocket connections"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.interaction_connections: Dict[str, str] = {}  # interaction_id -> connection_id
    
    async def connect(self, websocket: WebSocket, connection_id: str):
        """Accept outdoor connection"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        logger.info(f"Outdoor connection established: {connection_id}")
    
    def disconnect(self, connection_id: str):
        """Remove outdoor connection"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        # Remove from interaction mapping
        for interaction_id, conn_id in list(self.interaction_connections.items()):
            if conn_id == connection_id:
                del self.interaction_connections[interaction_id]
        
        logger.info(f"Outdoor connection closed: {connection_id}")
    
    async def send_to_connection(self, connection_id: str, message: str):
        """Send message to specific connection"""
        if connection_id in self.active_connections:
            try:
                await self.active_connections[connection_id].send_text(message)
            except Exception as e:
                logger.error(f"Failed to send message to {connection_id}: {e}")
    
    async def send_to_interaction(self, interaction_id: str, message: str):
        """Send message to interaction"""
        if interaction_id in self.interaction_connections:
            connection_id = self.interaction_connections[interaction_id]
            await self.send_to_connection(connection_id, message)


# Global outdoor connection manager
outdoor_manager = OutdoorConnectionManager()


@router.get("/chat", response_class=HTMLResponse)
async def get_outdoor_chat():
    """Get outdoor chat interface"""
    return get_outdoor_chat_html()


@router.websocket("/ws")
async def outdoor_websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for outdoor interactions"""
    connection_id = f"outdoor_{id(websocket)}"
    await outdoor_manager.connect(websocket, connection_id)
    
    outdoor_bot = get_outdoor_bot()
    interaction_id = None
    
    try:
        # Start interaction automatically
        interaction_id = outdoor_bot.start_outdoor_interaction("Visitor at outdoor interface")
        outdoor_manager.interaction_connections[interaction_id] = connection_id
        
        # Send initial greeting
        status = outdoor_bot.get_interaction_status(interaction_id)
        initial_message = {
            "type": "greeting",
            "message": "Hello! I'm the AI security assistant for this home. How can I help you today?",
            "interaction_id": interaction_id,
            "timestamp": status["started_at"].isoformat() if status.get("started_at") else None
        }
        
        await outdoor_manager.send_to_connection(
            connection_id, 
            json.dumps(initial_message)
        )
        
        while True:
            # Receive message from visitor
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            visitor_message = message_data.get("message", "")
            visitor_name = message_data.get("visitor_name")
            
            if not visitor_message.strip():
                continue
            
            # Process message through outdoor bot
            try:
                # Check if we need to start form collection
                if outdoor_bot.check_permission_needed("home_visit", visitor_message, None):
                    # Check if form collection has started
                    if interaction_id not in outdoor_bot.form_collection_state:
                        # Start comprehensive form collection
                        form_response = outdoor_bot.start_form_collection(interaction_id)

                        response_data = {
                            "type": "form_collection_start",
                            "message": form_response,
                            "interaction_id": interaction_id,
                            "form_step": "basic_info"
                        }

                        await outdoor_manager.send_to_connection(
                            connection_id,
                            json.dumps(response_data)
                        )
                        continue

                    # Process form step
                    form_response, form_complete = outdoor_bot.process_form_step(
                        interaction_id, visitor_message
                    )

                    if form_complete:
                        # Form is complete, create enhanced permission request
                        response, needs_permission = outdoor_bot.process_outdoor_message(
                            interaction_id, "Form completed - requesting permission", visitor_name
                        )

                        response_data = {
                            "type": "form_complete",
                            "message": form_response + "\n\n" + response,
                            "needs_permission": True,
                            "interaction_id": interaction_id
                        }
                    else:
                        # Continue form collection
                        current_step = outdoor_bot.form_collection_state.get(interaction_id, "unknown")
                        response_data = {
                            "type": "form_collection",
                            "message": form_response,
                            "interaction_id": interaction_id,
                            "form_step": current_step
                        }

                    await outdoor_manager.send_to_connection(
                        connection_id,
                        json.dumps(response_data)
                    )

                else:
                    # Regular conversation processing
                    response, needs_permission = outdoor_bot.process_outdoor_message(
                        interaction_id, visitor_message, visitor_name
                    )

                    # Get updated status
                    status = outdoor_bot.get_interaction_status(interaction_id)

                    # Send response back
                    response_data = {
                        "type": "response",
                        "message": response,
                        "needs_permission": needs_permission,
                        "interaction_id": interaction_id,
                        "status": status,
                        "timestamp": status.get("started_at").isoformat() if status.get("started_at") else None
                    }

                    await outdoor_manager.send_to_connection(
                        connection_id,
                        json.dumps(response_data)
                    )

                # If permission was requested, send notification
                status = outdoor_bot.get_interaction_status(interaction_id)
                if status.get("permission_sent"):
                    permission_notification = {
                        "type": "permission_requested",
                        "message": "Your comprehensive request has been sent to our security team for review. Please wait for approval.",
                        "interaction_id": interaction_id
                    }

                    await outdoor_manager.send_to_connection(
                        connection_id,
                        json.dumps(permission_notification)
                    )
                
            except Exception as e:
                logger.error(f"Error processing outdoor message: {e}")
                error_response = {
                    "type": "error",
                    "message": "I apologize, but I'm experiencing technical difficulties. Please try again or contact security directly if this is urgent.",
                    "interaction_id": interaction_id
                }
                
                await outdoor_manager.send_to_connection(
                    connection_id,
                    json.dumps(error_response)
                )
    
    except WebSocketDisconnect:
        outdoor_manager.disconnect(connection_id)
        if interaction_id:
            outdoor_bot.end_interaction(interaction_id, "disconnected")
    
    except Exception as e:
        logger.error(f"Outdoor WebSocket error: {e}")
        outdoor_manager.disconnect(connection_id)
        if interaction_id:
            outdoor_bot.end_interaction(interaction_id, "error")


def get_outdoor_chat_html() -> str:
    """Generate outdoor chat interface HTML"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>HISS Outdoor Security Assistant</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .chat-container { 
                max-width: 600px; 
                margin: 0 auto; 
                background: white; 
                border-radius: 15px; 
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                overflow: hidden;
            }
            .chat-header { 
                background: linear-gradient(135deg, #2c3e50, #34495e);
                color: white; 
                padding: 25px; 
                text-align: center; 
            }
            .chat-header h2 { margin: 0; font-size: 1.5em; }
            .chat-header p { margin: 10px 0 0 0; opacity: 0.9; }
            .chat-messages { 
                height: 450px; 
                overflow-y: auto; 
                padding: 20px; 
                background: #f8f9fa;
            }
            .message { 
                margin: 15px 0; 
                padding: 12px 16px; 
                border-radius: 18px; 
                max-width: 80%;
                word-wrap: break-word;
            }
            .visitor-message { 
                background: #007bff; 
                color: white; 
                margin-left: auto; 
                text-align: right;
            }
            .bot-message { 
                background: #e9ecef; 
                color: #2c3e50; 
                margin-right: auto;
            }
            .system-message {
                background: #28a745;
                color: white;
                text-align: center;
                margin: 10px auto;
                font-style: italic;
                max-width: 90%;
            }
            .chat-input-container { 
                padding: 20px; 
                background: white;
                border-top: 1px solid #dee2e6;
            }
            .chat-input { 
                display: flex; 
                gap: 10px;
            }
            .chat-input input { 
                flex: 1; 
                padding: 12px 16px; 
                border: 2px solid #dee2e6; 
                border-radius: 25px; 
                font-size: 16px;
                outline: none;
            }
            .chat-input input:focus {
                border-color: #007bff;
            }
            .chat-input button { 
                padding: 12px 24px; 
                background: #007bff; 
                color: white; 
                border: none; 
                border-radius: 25px; 
                cursor: pointer;
                font-size: 16px;
                font-weight: bold;
            }
            .chat-input button:hover { 
                background: #0056b3; 
            }
            .chat-input button:disabled {
                background: #6c757d;
                cursor: not-allowed;
            }
            .status-indicator {
                padding: 10px 20px;
                text-align: center;
                font-size: 14px;
                background: #d4edda;
                color: #155724;
                border-bottom: 1px solid #c3e6cb;
            }
            .status-waiting {
                background: #fff3cd;
                color: #856404;
            }
            .status-error {
                background: #f8d7da;
                color: #721c24;
            }
            .status-form {
                background: #cce5ff;
                color: #004085;
            }
            .form-progress {
                padding: 10px 20px;
                background: #e3f2fd;
                border-bottom: 1px solid #bbdefb;
                font-size: 14px;
            }
            .progress-bar {
                width: 100%;
                height: 6px;
                background: #e0e0e0;
                border-radius: 3px;
                margin: 5px 0;
            }
            .progress-fill {
                height: 100%;
                background: #2196f3;
                border-radius: 3px;
                transition: width 0.3s ease;
            }
            .visitor-info {
                padding: 15px 20px;
                background: #e3f2fd;
                border-bottom: 1px solid #bbdefb;
            }
            .visitor-info input {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ccc;
                border-radius: 4px;
                margin-top: 5px;
            }
            .timestamp {
                font-size: 0.8em;
                opacity: 0.7;
                margin-top: 5px;
            }
        </style>
    </head>
    <body>
        <div class="chat-container">
            <div class="chat-header">
                <h2>🏠 Home Security Assistant</h2>
                <p>AI-powered outdoor security system</p>
            </div>
            
            <div class="status-indicator" id="status">
                Connecting to security system...
            </div>

            <div class="form-progress" id="formProgress" style="display: none;">
                <div>Information Collection Progress</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <div id="formStep">Step 1 of 10: Basic Information</div>
            </div>

            <div class="visitor-info" id="visitorInfo" style="display: none;">
                <label for="visitorName">Your name (optional):</label>
                <input type="text" id="visitorName" placeholder="Enter your name to help us assist you better">
            </div>
            
            <div class="chat-messages" id="messages"></div>
            
            <div class="chat-input-container">
                <div class="chat-input">
                    <input type="text" id="messageInput" placeholder="Type your message here..." disabled>
                    <button id="sendButton" onclick="sendMessage()" disabled>Send</button>
                </div>
            </div>
        </div>

        <script>
            let ws = null;
            let interactionId = null;
            let isConnected = false;
            let isFormCollection = false;
            let currentFormStep = null;

            const messages = document.getElementById('messages');
            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const status = document.getElementById('status');
            const visitorInfo = document.getElementById('visitorInfo');
            const visitorName = document.getElementById('visitorName');
            const formProgress = document.getElementById('formProgress');
            const progressFill = document.getElementById('progressFill');
            const formStep = document.getElementById('formStep');

            function connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/outdoor/ws`;
                
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    isConnected = true;
                    status.textContent = "Connected to security system";
                    status.className = "status-indicator";
                    messageInput.disabled = false;
                    sendButton.disabled = false;
                    visitorInfo.style.display = "block";
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                };
                
                ws.onclose = function(event) {
                    isConnected = false;
                    status.textContent = "Disconnected from security system";
                    status.className = "status-indicator status-error";
                    messageInput.disabled = true;
                    sendButton.disabled = true;
                    
                    // Try to reconnect after 3 seconds
                    setTimeout(connectWebSocket, 3000);
                };
                
                ws.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    status.textContent = "Connection error - retrying...";
                    status.className = "status-indicator status-error";
                };
            }

            function handleMessage(data) {
                switch(data.type) {
                    case 'greeting':
                        interactionId = data.interaction_id;
                        addMessage(data.message, 'bot', data.timestamp);
                        status.textContent = "Security assistant ready";
                        break;

                    case 'form_collection_start':
                        interactionId = data.interaction_id;
                        isFormCollection = true;
                        currentFormStep = data.form_step;
                        addMessage(data.message, 'bot');
                        showFormProgress(1);
                        status.textContent = "Collecting visitor information for security";
                        status.className = "status-indicator status-form";
                        break;

                    case 'form_collection':
                        addMessage(data.message, 'bot');
                        updateFormProgress(data.form_step);
                        status.textContent = "Please provide the requested information";
                        status.className = "status-indicator status-form";
                        break;

                    case 'form_complete':
                        isFormCollection = false;
                        addMessage(data.message, 'bot');
                        hideFormProgress();
                        status.textContent = "Information complete - requesting approval";
                        status.className = "status-indicator status-waiting";
                        break;

                    case 'response':
                        addMessage(data.message, 'bot', data.timestamp);
                        if (data.needs_permission) {
                            status.textContent = "Permission request sent - waiting for approval";
                            status.className = "status-indicator status-waiting";
                        }
                        break;

                    case 'permission_requested':
                        addMessage(data.message, 'system');
                        status.textContent = "Waiting for security team approval...";
                        status.className = "status-indicator status-waiting";
                        break;

                    case 'error':
                        addMessage(data.message, 'bot');
                        status.textContent = "System error - please try again";
                        status.className = "status-indicator status-error";
                        break;
                }
            }

            function showFormProgress(step) {
                formProgress.style.display = "block";
                updateFormProgress(getFormStepName(step));
            }

            function hideFormProgress() {
                formProgress.style.display = "none";
            }

            function updateFormProgress(stepName) {
                const steps = {
                    'basic_info': { num: 1, name: 'Basic Information' },
                    'identification': { num: 2, name: 'Identification' },
                    'address': { num: 3, name: 'Address Details' },
                    'visit_details': { num: 4, name: 'Visit Information' },
                    'appointment': { num: 5, name: 'Appointment Details' },
                    'vehicle': { num: 6, name: 'Transportation' },
                    'emergency_contact': { num: 7, name: 'Emergency Contact' },
                    'additional_info': { num: 8, name: 'Additional Information' },
                    'security_info': { num: 9, name: 'Security Questions' },
                    'verification': { num: 10, name: 'Final Verification' }
                };

                const step = steps[stepName] || { num: 1, name: 'Information Collection' };
                const progress = (step.num / 10) * 100;

                progressFill.style.width = progress + '%';
                formStep.textContent = `Step ${step.num} of 10: ${step.name}`;
            }

            function getFormStepName(stepNum) {
                const stepNames = [
                    'basic_info', 'identification', 'address', 'visit_details',
                    'appointment', 'vehicle', 'emergency_contact', 'additional_info',
                    'security_info', 'verification'
                ];
                return stepNames[stepNum - 1] || 'basic_info';
            }

            function sendMessage() {
                const message = messageInput.value.trim();
                if (!message || !isConnected) return;
                
                addMessage(message, 'visitor');
                
                const messageData = {
                    message: message,
                    visitor_name: visitorName.value.trim() || null
                };
                
                ws.send(JSON.stringify(messageData));
                messageInput.value = "";
            }

            function addMessage(text, sender, timestamp = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                
                const messageText = document.createElement('div');
                messageText.textContent = text;
                messageDiv.appendChild(messageText);
                
                if (timestamp) {
                    const timestampDiv = document.createElement('div');
                    timestampDiv.className = 'timestamp';
                    timestampDiv.textContent = new Date(timestamp).toLocaleTimeString();
                    messageDiv.appendChild(timestampDiv);
                }
                
                messages.appendChild(messageDiv);
                messages.scrollTop = messages.scrollHeight;
            }

            function handleKeyPress(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    sendMessage();
                }
            }

            // Initialize
            messageInput.addEventListener('keypress', handleKeyPress);
            connectWebSocket();
        </script>
    </body>
    </html>
    """
