"""
Document loader system for HISS
Handles loading and processing of various document types
"""

import os
from typing import List, Dict, Any, Optional
from pathlib import Path
from langchain.schema import Document
from langchain_community.document_loaders import (
    TextLoader,
    PyPDFLoader,
    Docx2txtLoader,
    DirectoryLoader
)

from ..utils.config import get_settings
from ..utils.logger import get_logger
from .vector_store import get_vector_store

settings = get_settings()
logger = get_logger(__name__)


class HissDocumentLoader:
    """Document loader for HISS security system"""
    
    def __init__(self):
        self.vector_store = get_vector_store()
        self.supported_extensions = {
            '.txt': TextLoader,
            '.pdf': PyPDFLoader,
            '.docx': Docx2txtLoader,
        }
    
    def load_document(self, file_path: str, metadata: Dict[str, Any] = None) -> List[Document]:
        """Load a single document"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            extension = file_path.suffix.lower()
            if extension not in self.supported_extensions:
                raise ValueError(f"Unsupported file type: {extension}")
            
            loader_class = self.supported_extensions[extension]
            loader = loader_class(str(file_path))
            documents = loader.load()
            
            # Add metadata to documents
            if metadata:
                for doc in documents:
                    doc.metadata.update(metadata)
            
            # Add file-specific metadata
            for doc in documents:
                doc.metadata.update({
                    'source_file': str(file_path),
                    'file_type': extension,
                    'file_name': file_path.name
                })
            
            logger.info(f"Loaded {len(documents)} documents from {file_path}")
            return documents
            
        except Exception as e:
            logger.error(f"Failed to load document {file_path}: {e}")
            raise
    
    def load_directory(
        self, 
        directory_path: str, 
        glob_pattern: str = "**/*",
        metadata: Dict[str, Any] = None
    ) -> List[Document]:
        """Load all supported documents from a directory"""
        try:
            directory_path = Path(directory_path)
            if not directory_path.exists():
                raise FileNotFoundError(f"Directory not found: {directory_path}")
            
            all_documents = []
            
            # Get all files matching the pattern
            for file_path in directory_path.glob(glob_pattern):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                    try:
                        documents = self.load_document(str(file_path), metadata)
                        all_documents.extend(documents)
                    except Exception as e:
                        logger.warning(f"Skipped file {file_path}: {e}")
                        continue
            
            logger.info(f"Loaded {len(all_documents)} documents from directory {directory_path}")
            return all_documents
            
        except Exception as e:
            logger.error(f"Failed to load directory {directory_path}: {e}")
            raise
    
    def load_security_protocols(self) -> List[Document]:
        """Load security protocol documents"""
        protocols_dir = Path("./data/documents/protocols")
        if not protocols_dir.exists():
            logger.warning("Security protocols directory not found")
            return []
        
        metadata = {
            'context_type': 'security_protocol',
            'category': 'policy'
        }
        
        return self.load_directory(str(protocols_dir), metadata=metadata)
    
    def load_visitor_guidelines(self) -> List[Document]:
        """Load visitor guideline documents"""
        guidelines_dir = Path("./data/documents/guidelines")
        if not guidelines_dir.exists():
            logger.warning("Visitor guidelines directory not found")
            return []
        
        metadata = {
            'context_type': 'visitor_guidelines',
            'category': 'guidelines'
        }
        
        return self.load_directory(str(guidelines_dir), metadata=metadata)
    
    def load_emergency_procedures(self) -> List[Document]:
        """Load emergency procedure documents"""
        emergency_dir = Path("./data/documents/emergency")
        if not emergency_dir.exists():
            logger.warning("Emergency procedures directory not found")
            return []
        
        metadata = {
            'context_type': 'emergency_procedures',
            'category': 'emergency'
        }
        
        return self.load_directory(str(emergency_dir), metadata=metadata)
    
    def load_all_knowledge_base(self) -> List[Document]:
        """Load all knowledge base documents"""
        all_documents = []
        
        # Load different types of documents
        document_types = [
            self.load_security_protocols,
            self.load_visitor_guidelines,
            self.load_emergency_procedures
        ]
        
        for load_func in document_types:
            try:
                documents = load_func()
                all_documents.extend(documents)
            except Exception as e:
                logger.error(f"Failed to load documents with {load_func.__name__}: {e}")
        
        logger.info(f"Loaded total of {len(all_documents)} documents to knowledge base")
        return all_documents
    
    def ingest_documents_to_vector_store(self, documents: List[Document]) -> List[str]:
        """Ingest documents into the vector store"""
        try:
            if not documents:
                logger.warning("No documents to ingest")
                return []
            
            doc_ids = self.vector_store.add_documents(documents)
            logger.info(f"Ingested {len(documents)} documents into vector store")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Failed to ingest documents: {e}")
            raise
    
    def initialize_knowledge_base(self) -> bool:
        """Initialize the knowledge base with all available documents"""
        try:
            logger.info("Initializing knowledge base...")
            
            # Load all documents
            all_documents = self.load_all_knowledge_base()
            
            if not all_documents:
                logger.warning("No documents found to initialize knowledge base")
                return False
            
            # Ingest into vector store
            self.ingest_documents_to_vector_store(all_documents)
            
            # Get collection info
            info = self.vector_store.get_collection_info()
            logger.info(f"Knowledge base initialized with {info.get('document_count', 0)} document chunks")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize knowledge base: {e}")
            return False


# Global document loader instance
_document_loader_instance = None


def get_document_loader() -> HissDocumentLoader:
    """Get the global document loader instance"""
    global _document_loader_instance
    if _document_loader_instance is None:
        _document_loader_instance = HissDocumentLoader()
    return _document_loader_instance
