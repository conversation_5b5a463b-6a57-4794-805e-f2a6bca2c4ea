"""
HISS Admin Hierarchy Demo
Demonstrates the enhanced admin/subadmin features and outdoor interaction workflow
"""

import asyncio
import time
from datetime import datetime
from python_client import HissClient


class AdminHierarchyDemo:
    """Demo class for HISS admin hierarchy features"""
    
    def __init__(self):
        self.client = HissClient()
    
    def print_header(self, title: str):
        """Print a formatted header"""
        print(f"\n{'='*70}")
        print(f"🛡️  {title}")
        print(f"{'='*70}")
    
    def print_step(self, step: str, description: str):
        """Print a formatted step"""
        print(f"\n📋 Step {step}: {description}")
        print("-" * 50)
    
    def demo_outdoor_interaction_workflow(self):
        """Demonstrate the complete outdoor interaction workflow"""
        self.print_header("OUTDOOR INTERACTION WORKFLOW")
        
        try:
            # Step 1: Start outdoor interaction
            self.print_step("1", "Visitor approaches the front door")
            
            response = self.client.session.post(
                f"{self.client.base_url}/outdoor/start",
                json={"visitor_description": "Person at front door, appears to be delivery person"}
            )
            response.raise_for_status()
            data = response.json()
            
            interaction_id = data["data"]["interaction_id"]
            print(f"✅ Outdoor interaction started: {interaction_id[:8]}...")
            
            # Step 2: Initial bot greeting and conversation
            self.print_step("2", "Bot greets visitor and starts conversation")
            
            conversation_flow = [
                {
                    "message": "Hello, I have a package delivery for John Smith",
                    "visitor_name": "Mike the Delivery Guy"
                },
                {
                    "message": "Yes, it's from Amazon. I need someone to sign for it",
                    "visitor_name": "Mike the Delivery Guy"
                },
                {
                    "message": "Actually, I also wanted to ask if John is home. I'm his friend from college",
                    "visitor_name": "Mike the Delivery Guy"
                }
            ]
            
            for i, interaction in enumerate(conversation_flow, 1):
                print(f"\n👤 Visitor: {interaction['message']}")
                
                response = self.client.session.post(
                    f"{self.client.base_url}/outdoor/{interaction_id}/message",
                    json={
                        "message": interaction["message"],
                        "visitor_name": interaction["visitor_name"]
                    }
                )
                response.raise_for_status()
                data = response.json()
                
                bot_response = data["data"]["response"]
                needs_permission = data["data"]["needs_permission"]
                
                print(f"🤖 HISS Bot: {bot_response}")
                
                if needs_permission:
                    print("🔔 Permission request sent to SubAdmin!")
                    break
                
                time.sleep(2)
            
            # Step 3: Check interaction status
            self.print_step("3", "Checking interaction status")
            
            response = self.client.session.get(
                f"{self.client.base_url}/outdoor/{interaction_id}/status"
            )
            response.raise_for_status()
            status_data = response.json()
            
            status = status_data["data"]
            print(f"✅ Interaction Status:")
            print(f"   Visitor: {status.get('visitor_name', 'Unknown')}")
            print(f"   Location: {status.get('location', 'Unknown')}")
            print(f"   Messages: {status.get('total_messages', 0)}")
            print(f"   Permission Needed: {status.get('permission_needed', False)}")
            print(f"   Permission Sent: {status.get('permission_sent', False)}")
            
            return interaction_id
            
        except Exception as e:
            print(f"❌ Error in outdoor interaction demo: {e}")
            return None
    
    def demo_subadmin_workflow(self):
        """Demonstrate SubAdmin permission workflow"""
        self.print_header("SUBADMIN PERMISSION WORKFLOW")
        
        try:
            # Step 1: Check pending requests
            self.print_step("1", "SubAdmin checks pending permission requests")
            
            response = self.client.session.get(
                f"{self.client.base_url}/subadmin/requests/pending",
                params={"user_id": "subadmin_demo"}  # Simplified auth
            )
            
            if response.status_code == 200:
                data = response.json()
                pending_requests = data["data"]["requests"]
                
                print(f"✅ Found {len(pending_requests)} pending requests")
                
                for request in pending_requests:
                    print(f"\n📋 Request Details:")
                    print(f"   Visitor: {request['visitor_name']}")
                    print(f"   Purpose: {request['visitor_purpose']}")
                    print(f"   Bot Summary: {request['bot_conversation_summary']}")
                    print(f"   Bot Recommendation: {request['bot_recommendation']}")
                    print(f"   Confidence: {request['bot_confidence']:.2f}")
                    
                    # Step 2: SubAdmin makes decision
                    self.print_step("2", "SubAdmin reviews and makes decision")
                    
                    # For demo, approve the first request
                    if pending_requests:
                        request_id = pending_requests[0]["id"]
                        
                        print("🤔 SubAdmin reviewing request...")
                        time.sleep(2)
                        
                        # Approve the request
                        approve_response = self.client.session.post(
                            f"{self.client.base_url}/subadmin/requests/{request_id}/approve",
                            json={"reason": "Legitimate delivery with valid purpose"},
                            params={"user_id": "subadmin_demo"}
                        )
                        
                        if approve_response.status_code == 200:
                            print("✅ Request APPROVED by SubAdmin")
                            print("   Reason: Legitimate delivery with valid purpose")
                        else:
                            print("❌ Failed to approve request")
            else:
                print("⚠️  Could not access SubAdmin panel (authentication required)")
                
        except Exception as e:
            print(f"❌ Error in SubAdmin workflow demo: {e}")
    
    def demo_admin_dashboard(self):
        """Demonstrate Admin Dashboard features"""
        self.print_header("ADMIN DASHBOARD FEATURES")
        
        try:
            # Step 1: View system analytics
            self.print_step("1", "Admin views system analytics")
            
            response = self.client.session.get(
                f"{self.client.base_url}/admin/analytics/summary",
                params={"user_id": "admin_demo", "days": 7}
            )
            
            if response.status_code == 200:
                data = response.json()
                analytics = data["data"]
                
                print(f"📊 System Analytics (Last 7 days):")
                print(f"   Total Visitors: {analytics['visitors']['total']}")
                print(f"   Recent Visitors: {analytics['visitors']['recent']}")
                print(f"   Total Decisions: {analytics['decisions']['total']}")
                print(f"   Approval Rate: {analytics['decisions']['approval_rate']:.1f}%")
                print(f"   Active Sessions: {analytics['conversations']['active_sessions']}")
                print(f"   Total Messages: {analytics['conversations']['total_messages']}")
            
            # Step 2: View all conversations
            self.print_step("2", "Admin views conversation history")
            
            response = self.client.session.get(
                f"{self.client.base_url}/admin/conversations/all",
                params={"user_id": "admin_demo", "limit": 5}
            )
            
            if response.status_code == 200:
                data = response.json()
                conversations = data["data"]["conversations"]
                
                print(f"💬 Recent Conversations ({len(conversations)} shown):")
                for conv in conversations[:3]:  # Show first 3
                    print(f"   [{conv['timestamp'][:19]}] {conv['role']}: {conv['content'][:60]}...")
            
            # Step 3: View admin actions
            self.print_step("3", "Admin views action history")
            
            response = self.client.session.get(
                f"{self.client.base_url}/admin/actions/history",
                params={"user_id": "admin_demo", "limit": 5}
            )
            
            if response.status_code == 200:
                data = response.json()
                actions = data["data"]["actions"]
                
                print(f"📋 Recent Admin Actions ({len(actions)} shown):")
                for action in actions[:3]:  # Show first 3
                    print(f"   [{action['timestamp'][:19]}] {action['admin_name']}: {action['description']}")
            
        except Exception as e:
            print(f"❌ Error in admin dashboard demo: {e}")
    
    def demo_real_time_notifications(self):
        """Demonstrate real-time notification system"""
        self.print_header("REAL-TIME NOTIFICATION SYSTEM")
        
        try:
            # Step 1: Check system alerts
            self.print_step("1", "Checking system alerts")
            
            response = self.client.session.get(
                f"{self.client.base_url}/subadmin/alerts",
                params={"user_id": "subadmin_demo"}
            )
            
            if response.status_code == 200:
                data = response.json()
                alerts = data["data"]["alerts"]
                unread_count = data["data"]["unread_count"]
                
                print(f"🔔 System Alerts:")
                print(f"   Total Alerts: {len(alerts)}")
                print(f"   Unread: {unread_count}")
                
                for alert in alerts[:3]:  # Show first 3
                    severity_emoji = {
                        "critical": "🚨",
                        "high": "⚠️",
                        "medium": "ℹ️",
                        "low": "📝"
                    }.get(alert["severity"], "📝")
                    
                    print(f"   {severity_emoji} [{alert['severity'].upper()}] {alert['title']}")
                    print(f"      {alert['message']}")
            
            print("\n🔄 Real-time features:")
            print("   • SubAdmin panel auto-refreshes every 15 seconds")
            print("   • Admin dashboard auto-refreshes every 30 seconds")
            print("   • WebSocket notifications for urgent alerts")
            print("   • Background task processing for permissions")
            
        except Exception as e:
            print(f"❌ Error in notification demo: {e}")
    
    def demo_complete_workflow(self):
        """Demonstrate the complete workflow from outdoor to approval"""
        self.print_header("COMPLETE WORKFLOW DEMONSTRATION")
        
        print("🎯 This demo shows the complete flow:")
        print("   1. Visitor approaches outdoor")
        print("   2. Bot conducts natural conversation")
        print("   3. Bot detects need for home access")
        print("   4. Permission request sent to SubAdmin")
        print("   5. SubAdmin reviews and approves/denies")
        print("   6. Admin monitors all activity")
        print("   7. Real-time notifications throughout")
        
        # Run the complete workflow
        interaction_id = self.demo_outdoor_interaction_workflow()
        time.sleep(3)
        
        self.demo_subadmin_workflow()
        time.sleep(3)
        
        self.demo_admin_dashboard()
        time.sleep(3)
        
        self.demo_real_time_notifications()
        
        print(f"\n🎉 Complete workflow demonstration finished!")
        print(f"   Interaction ID: {interaction_id}")
        print(f"   All components working together seamlessly")
    
    def demo_access_interfaces(self):
        """Show how to access different interfaces"""
        self.print_header("ACCESS INTERFACES")
        
        print("🌐 Web Interfaces Available:")
        print(f"   • Outdoor Chat: http://localhost:8000/chat")
        print(f"   • Admin Dashboard: http://localhost:8000/admin/dashboard")
        print(f"   • SubAdmin Panel: http://localhost:8000/subadmin/panel")
        print(f"   • API Documentation: http://localhost:8000/docs")
        print(f"   • System Health: http://localhost:8000/health")
        
        print(f"\n🔑 Default Credentials (Demo Only):")
        print(f"   Admin: admin / admin123")
        print(f"   SubAdmin: subadmin / subadmin123")
        
        print(f"\n📱 API Endpoints:")
        print(f"   • POST /outdoor/start - Start outdoor interaction")
        print(f"   • POST /outdoor/{{id}}/message - Send message")
        print(f"   • GET /subadmin/requests/pending - Get pending requests")
        print(f"   • POST /subadmin/requests/{{id}}/approve - Approve request")
        print(f"   • GET /admin/conversations/all - View all conversations")


def main():
    """Main demo function"""
    demo = AdminHierarchyDemo()
    
    print("🛡️ HISS Admin Hierarchy & Outdoor Interaction Demo")
    print("=" * 60)
    
    try:
        # Check if system is running
        status = demo.client.get_system_status()
        print(f"✅ System Status: {status['message']}")
        
        # Show access interfaces
        demo.demo_access_interfaces()
        
        # Run complete workflow demo
        demo.demo_complete_workflow()
        
        print(f"\n🎊 Demo completed successfully!")
        print(f"   The enhanced HISS system now includes:")
        print(f"   ✅ Admin hierarchy with role-based permissions")
        print(f"   ✅ Outdoor bot for natural visitor interactions")
        print(f"   ✅ SubAdmin permission approval workflow")
        print(f"   ✅ Real-time notifications and alerts")
        print(f"   ✅ Comprehensive conversation history")
        print(f"   ✅ Web-based admin and subadmin interfaces")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        print(f"   Make sure HISS is running: python main.py")


if __name__ == "__main__":
    main()
