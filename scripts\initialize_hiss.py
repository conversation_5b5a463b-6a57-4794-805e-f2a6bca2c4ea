"""
HISS Initialization Script
Sets up the complete HISS system with sample data and knowledge base
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.fake_data import initialize_sample_data
from src.database.document_loader import get_document_loader
from src.database.vector_store import get_vector_store
from src.utils.config import get_settings, ensure_directories
from src.utils.logger import get_logger

logger = get_logger(__name__)


def check_environment():
    """Check if environment is properly configured"""
    print("🔍 Checking environment configuration...")
    
    settings = get_settings()
    
    # Check for required environment variables
    required_vars = ['GOOGLE_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not getattr(settings, var.lower(), None):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("   Please check your .env file and ensure all required variables are set.")
        return False
    
    print("✅ Environment configuration looks good!")
    return True


def setup_directories():
    """Setup required directories"""
    print("📁 Setting up directories...")
    
    try:
        ensure_directories()
        
        # Additional directories
        additional_dirs = [
            "./logs",
            "./data/documents/protocols",
            "./data/documents/guidelines", 
            "./data/documents/emergency",
            "./data/fake_data"
        ]
        
        for directory in additional_dirs:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        print("✅ All directories created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to setup directories: {e}")
        return False


def initialize_sample_documents():
    """Initialize sample documents and data"""
    print("📄 Initializing sample documents...")
    
    try:
        initialize_sample_data()
        print("✅ Sample documents and data created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize sample documents: {e}")
        return False


def setup_vector_store():
    """Setup and initialize vector store"""
    print("🗄️  Setting up vector store...")
    
    try:
        vector_store = get_vector_store()
        info = vector_store.get_collection_info()
        print(f"✅ Vector store initialized: {info.get('name', 'Unknown')}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to setup vector store: {e}")
        return False


def load_knowledge_base():
    """Load documents into the knowledge base"""
    print("📚 Loading knowledge base...")
    
    try:
        document_loader = get_document_loader()
        success = document_loader.initialize_knowledge_base()
        
        if success:
            vector_store = get_vector_store()
            info = vector_store.get_collection_info()
            doc_count = info.get('document_count', 0)
            print(f"✅ Knowledge base loaded with {doc_count} document chunks!")
            return True
        else:
            print("⚠️  Knowledge base initialization completed with warnings")
            return True
            
    except Exception as e:
        print(f"❌ Failed to load knowledge base: {e}")
        return False


def verify_system():
    """Verify system is working correctly"""
    print("🔧 Verifying system components...")
    
    try:
        # Test vector store
        vector_store = get_vector_store()
        test_docs = vector_store.similarity_search("security protocol", k=1)
        print(f"✅ Vector store search working (found {len(test_docs)} documents)")
        
        # Test LLM (basic import test)
        from src.utils.llm import get_llm
        llm = get_llm()
        print("✅ LLM integration loaded successfully")
        
        # Test security agent (basic import test)
        from src.agents.security_agent import get_security_agent
        agent = get_security_agent()
        print("✅ Security agent loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ System verification failed: {e}")
        return False


def print_startup_info():
    """Print startup information and next steps"""
    settings = get_settings()
    
    print("\n" + "="*60)
    print("🛡️  HISS INITIALIZATION COMPLETE")
    print("="*60)
    
    print(f"\n📋 System Configuration:")
    print(f"   • System Name: {settings.system_name}")
    print(f"   • Security Level: {settings.security_level}")
    print(f"   • API Host: {settings.api_host}")
    print(f"   • API Port: {settings.api_port}")
    print(f"   • Model: {settings.model_name}")
    print(f"   • Vector DB: {settings.chroma_db_path}")
    
    print(f"\n🚀 Next Steps:")
    print(f"   1. Start the system: python main.py")
    print(f"   2. Open API docs: http://{settings.api_host}:{settings.api_port}/docs")
    print(f"   3. Try chat interface: http://{settings.api_host}:{settings.api_port}/chat")
    print(f"   4. Run demo: python examples/demo_script.py")
    
    print(f"\n📖 Documentation:")
    print(f"   • Setup Guide: docs/SETUP_GUIDE.md")
    print(f"   • API Docs: docs/API_DOCUMENTATION.md")
    print(f"   • Examples: examples/")
    
    print(f"\n🔧 Troubleshooting:")
    print(f"   • Check logs: {settings.log_file}")
    print(f"   • Reset system: curl -X POST http://localhost:{settings.api_port}/system/reset")
    
    print("\n✨ HISS is ready to secure your facility!")


def main():
    """Main initialization function"""
    print("🛡️  HISS System Initialization")
    print("="*50)
    
    steps = [
        ("Environment Check", check_environment),
        ("Directory Setup", setup_directories),
        ("Sample Documents", initialize_sample_documents),
        ("Vector Store Setup", setup_vector_store),
        ("Knowledge Base Loading", load_knowledge_base),
        ("System Verification", verify_system)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ {step_name} failed with exception: {e}")
            failed_steps.append(step_name)
    
    if failed_steps:
        print(f"\n⚠️  Initialization completed with issues:")
        for step in failed_steps:
            print(f"   • {step}")
        print("\nPlease check the errors above and retry if necessary.")
    else:
        print("\n🎉 All initialization steps completed successfully!")
    
    print_startup_info()
    
    return len(failed_steps) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
