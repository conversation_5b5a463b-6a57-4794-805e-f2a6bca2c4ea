"""
Database configuration for HISS with Neon PostgreSQL
"""

import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import MetaData

from ..utils.config import get_settings
from ..utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


class Base(DeclarativeBase):
    """Base class for all database models"""
    metadata = MetaData(
        naming_convention={
            "ix": "ix_%(column_0_label)s",
            "uq": "uq_%(table_name)s_%(column_0_name)s",
            "ck": "ck_%(table_name)s_%(constraint_name)s",
            "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
            "pk": "pk_%(table_name)s"
        }
    )


# Convert PostgreSQL URL to async format
def get_async_database_url(database_url: str) -> str:
    """Convert PostgreSQL URL to async format for asyncpg"""
    # Handle SSL parameters that asyncpg doesn't recognize in URL
    if "sslmode=require" in database_url:
        # Remove sslmode and channel_binding from URL, handle via connect_args
        import urllib.parse
        parsed = urllib.parse.urlparse(database_url)

        # Remove SSL parameters from query string
        query_params = urllib.parse.parse_qs(parsed.query)
        query_params.pop('sslmode', None)
        query_params.pop('channel_binding', None)

        # Rebuild URL without SSL parameters
        new_query = urllib.parse.urlencode(query_params, doseq=True)
        new_parsed = parsed._replace(query=new_query)
        clean_url = urllib.parse.urlunparse(new_parsed)
    else:
        clean_url = database_url

    # Convert to async format
    if clean_url.startswith("postgresql://"):
        return clean_url.replace("postgresql://", "postgresql+asyncpg://", 1)
    elif clean_url.startswith("postgres://"):
        return clean_url.replace("postgres://", "postgresql+asyncpg://", 1)
    return clean_url

# Create async engine with SSL support
async_database_url = get_async_database_url(settings.database_url)

# SSL configuration for Neon
connect_args = {}
if "neon.tech" in settings.database_url:
    connect_args = {
        "ssl": "require",
        "server_settings": {
            "application_name": "HISS_Security_System",
        }
    }

engine = create_async_engine(
    async_database_url,
    echo=False,  # Set to True for SQL debugging
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
    connect_args=connect_args
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db():
    """Initialize database tables"""
    try:
        async with engine.begin() as conn:
            # Import all models to ensure they're registered
            from . import models
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            
        logger.info("Database tables initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False


async def check_db_connection():
    """Check database connection"""
    try:
        from sqlalchemy import text
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            result.scalar()
        logger.info("Database connection successful")
        return True

    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


async def close_db():
    """Close database connections"""
    await engine.dispose()
    logger.info("Database connections closed")
