"""
SubAdmin Panel for HISS
Handles permission requests and visitor approval workflow
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import HTMLResponse
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..models.admin import User, UserRole, PermissionRequest, RequestStatus, SystemAlert
from ..models.security import VisitorInfo
from ..utils.logger import get_logger, log_security_event

logger = get_logger(__name__)

# Global storage (in production, use proper database)
permission_requests_db: Dict[str, PermissionRequest] = {}
system_alerts_db: List[SystemAlert] = []

router = APIRouter(prefix="/subadmin", tags=["subadmin"])


def get_current_subadmin(user_id: str = None) -> User:
    """Get current subadmin user (simplified auth)"""
    from .admin_dashboard import admin_users_db
    
    if not user_id or user_id not in admin_users_db:
        raise HTTPException(status_code=401, detail="SubAdmin authentication required")
    
    user = admin_users_db[user_id]
    if user.role not in [UserRole.SUBADMIN, UserRole.ADMIN]:
        raise HTTPException(status_code=403, detail="SubAdmin privileges required")
    
    return user


def create_system_alert(alert_type: str, title: str, message: str, severity: str = "medium", related_id: str = None):
    """Create system alert"""
    alert = SystemAlert(
        alert_type=alert_type,
        severity=severity,
        title=title,
        message=message,
        related_id=related_id
    )
    system_alerts_db.append(alert)
    return alert


@router.get("/panel", response_class=HTMLResponse)
async def get_subadmin_panel():
    """Get subadmin panel HTML"""
    return get_subadmin_panel_html()


@router.get("/requests/pending")
async def get_pending_requests(
    subadmin: User = Depends(get_current_subadmin)
):
    """Get pending permission requests"""
    
    try:
        pending_requests = [
            req for req in permission_requests_db.values() 
            if req.status == RequestStatus.PENDING
        ]
        
        # Sort by priority and timestamp
        pending_requests.sort(key=lambda x: (-x.priority, x.requested_at))
        
        return {
            "success": True,
            "data": {
                "requests": pending_requests,
                "count": len(pending_requests)
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get pending requests: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve requests")


@router.post("/requests/{request_id}/approve")
async def approve_request(
    request_id: str,
    background_tasks: BackgroundTasks,
    reason: str = "Approved by SubAdmin",
    subadmin: User = Depends(get_current_subadmin)
):
    """Approve a permission request"""
    
    try:
        if request_id not in permission_requests_db:
            raise HTTPException(status_code=404, detail="Request not found")
        
        request = permission_requests_db[request_id]
        
        if request.status != RequestStatus.PENDING:
            raise HTTPException(status_code=400, detail="Request already processed")
        
        # Update request
        request.status = RequestStatus.APPROVED
        request.reviewed_by = subadmin.id
        request.reviewed_at = datetime.now()
        request.decision_reason = reason
        
        # Update visitor status
        from ..api.main import visitors_db
        if request.visitor_id in visitors_db:
            visitor = visitors_db[request.visitor_id]
            visitor.status = "approved"
        
        # Log security event
        log_security_event(
            event_type="permission_approved",
            visitor_id=request.visitor_id,
            decision="APPROVED",
            reason=reason,
            additional_data={
                "approved_by": subadmin.full_name,
                "request_id": request_id
            }
        )
        
        # Create alert for admin
        create_system_alert(
            alert_type="permission_approved",
            title="Visitor Approved",
            message=f"SubAdmin {subadmin.full_name} approved visitor {request.visitor_name}",
            severity="low",
            related_id=request_id
        )
        
        # Schedule notification to bot (background task)
        background_tasks.add_task(notify_bot_approval, request_id, True)
        
        return {
            "success": True,
            "message": "Request approved successfully",
            "data": {"request_id": request_id, "status": "approved"}
        }
        
    except Exception as e:
        logger.error(f"Failed to approve request: {e}")
        raise HTTPException(status_code=500, detail="Failed to approve request")


@router.post("/requests/{request_id}/deny")
async def deny_request(
    request_id: str,
    background_tasks: BackgroundTasks,
    reason: str = "Denied by SubAdmin",
    subadmin: User = Depends(get_current_subadmin)
):
    """Deny a permission request"""
    
    try:
        if request_id not in permission_requests_db:
            raise HTTPException(status_code=404, detail="Request not found")
        
        request = permission_requests_db[request_id]
        
        if request.status != RequestStatus.PENDING:
            raise HTTPException(status_code=400, detail="Request already processed")
        
        # Update request
        request.status = RequestStatus.DENIED
        request.reviewed_by = subadmin.id
        request.reviewed_at = datetime.now()
        request.decision_reason = reason
        
        # Update visitor status
        from ..api.main import visitors_db
        if request.visitor_id in visitors_db:
            visitor = visitors_db[request.visitor_id]
            visitor.status = "denied"
        
        # Log security event
        log_security_event(
            event_type="permission_denied",
            visitor_id=request.visitor_id,
            decision="DENIED",
            reason=reason,
            additional_data={
                "denied_by": subadmin.full_name,
                "request_id": request_id
            }
        )
        
        # Create alert for admin
        create_system_alert(
            alert_type="permission_denied",
            title="Visitor Denied",
            message=f"SubAdmin {subadmin.full_name} denied visitor {request.visitor_name}",
            severity="medium",
            related_id=request_id
        )
        
        # Schedule notification to bot (background task)
        background_tasks.add_task(notify_bot_approval, request_id, False)
        
        return {
            "success": True,
            "message": "Request denied successfully",
            "data": {"request_id": request_id, "status": "denied"}
        }
        
    except Exception as e:
        logger.error(f"Failed to deny request: {e}")
        raise HTTPException(status_code=500, detail="Failed to deny request")


@router.get("/requests/history")
async def get_request_history(
    subadmin: User = Depends(get_current_subadmin),
    limit: int = 50
):
    """Get request history"""
    
    try:
        all_requests = list(permission_requests_db.values())
        all_requests.sort(key=lambda x: x.requested_at, reverse=True)
        
        return {
            "success": True,
            "data": {
                "requests": all_requests[:limit],
                "total": len(all_requests)
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get request history: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve history")


@router.get("/alerts")
async def get_system_alerts(
    subadmin: User = Depends(get_current_subadmin),
    unread_only: bool = False
):
    """Get system alerts"""
    
    try:
        alerts = system_alerts_db
        
        if unread_only:
            alerts = [alert for alert in alerts if not alert.acknowledged]
        
        # Sort by severity and timestamp
        severity_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        alerts.sort(key=lambda x: (-severity_order.get(x.severity, 0), -x.created_at.timestamp()))
        
        return {
            "success": True,
            "data": {
                "alerts": alerts[:20],  # Limit to 20 most recent
                "unread_count": len([a for a in system_alerts_db if not a.acknowledged])
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve alerts")


@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(
    alert_id: str,
    subadmin: User = Depends(get_current_subadmin)
):
    """Acknowledge a system alert"""
    
    try:
        alert = next((a for a in system_alerts_db if a.id == alert_id), None)
        
        if not alert:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        alert.acknowledged = True
        alert.acknowledged_by = subadmin.id
        alert.acknowledged_at = datetime.now()
        
        return {
            "success": True,
            "message": "Alert acknowledged"
        }
        
    except Exception as e:
        logger.error(f"Failed to acknowledge alert: {e}")
        raise HTTPException(status_code=500, detail="Failed to acknowledge alert")


async def notify_bot_approval(request_id: str, approved: bool):
    """Notify bot about approval decision (background task)"""
    try:
        # This would integrate with the bot system to notify about the decision
        # For now, we'll just log it
        status = "approved" if approved else "denied"
        logger.info(f"Bot notified: Request {request_id} was {status}")
        
        # In a real implementation, this would:
        # 1. Send message to the bot system
        # 2. Update the outdoor interaction
        # 3. Notify the visitor waiting outside
        
    except Exception as e:
        logger.error(f"Failed to notify bot: {e}")


def create_permission_request(
    visitor_id: str,
    visitor_name: str,
    visitor_purpose: str,
    bot_summary: str,
    bot_recommendation: str,
    bot_confidence: float,
    visitor_details: Dict[str, Any] = None
) -> PermissionRequest:
    """Create a new permission request"""
    
    request = PermissionRequest(
        visitor_id=visitor_id,
        visitor_name=visitor_name,
        visitor_purpose=visitor_purpose,
        visitor_details=visitor_details or {},
        bot_conversation_summary=bot_summary,
        bot_recommendation=bot_recommendation,
        bot_confidence=bot_confidence,
        priority=3 if "urgent" in visitor_purpose.lower() else 1
    )
    
    permission_requests_db[request.id] = request
    
    # Create alert for new request
    create_system_alert(
        alert_type="permission_request",
        title="New Permission Request",
        message=f"Visitor {visitor_name} requests access: {visitor_purpose}",
        severity="medium",
        related_id=request.id
    )
    
    logger.info(f"Permission request created: {request.id} for visitor {visitor_name}")
    return request


def get_subadmin_panel_html() -> str:
    """Generate subadmin panel HTML"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>HISS SubAdmin Panel</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .header { background: #27ae60; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
            .panel-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 20px; }
            .card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .request-item { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0; }
            .request-pending { border-left: 4px solid #f39c12; }
            .request-approved { border-left: 4px solid #27ae60; }
            .request-denied { border-left: 4px solid #e74c3c; }
            .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
            .btn-approve { background: #27ae60; color: white; }
            .btn-deny { background: #e74c3c; color: white; }
            .btn-info { background: #3498db; color: white; }
            .btn:hover { opacity: 0.8; }
            .priority-high { background: #ffe6e6; }
            .priority-medium { background: #fff3e0; }
            .priority-low { background: #e8f5e8; }
            .alert-item { padding: 10px; margin: 5px 0; border-radius: 4px; }
            .alert-critical { background: #ffebee; border-left: 4px solid #f44336; }
            .alert-high { background: #fff3e0; border-left: 4px solid #ff9800; }
            .alert-medium { background: #e3f2fd; border-left: 4px solid #2196f3; }
            .alert-low { background: #f1f8e9; border-left: 4px solid #4caf50; }
            .stats { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-bottom: 20px; }
            .stat-card { background: white; padding: 15px; border-radius: 8px; text-align: center; }
            .stat-number { font-size: 2em; font-weight: bold; color: #27ae60; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🛡️ HISS SubAdmin Panel</h1>
            <p>Visitor Permission Management System</p>
            <button class="btn btn-info" onclick="refreshPanel()">🔄 Refresh</button>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="pendingCount">-</div>
                <div>Pending Requests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayApproved">-</div>
                <div>Approved Today</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="unreadAlerts">-</div>
                <div>Unread Alerts</div>
            </div>
        </div>

        <div class="panel-grid">
            <div class="card">
                <h3>🔔 Pending Permission Requests</h3>
                <div id="pendingRequests">Loading requests...</div>
            </div>

            <div class="card">
                <h3>⚠️ System Alerts</h3>
                <div id="systemAlerts">Loading alerts...</div>
                <button class="btn btn-info" onclick="loadAlerts()">View All Alerts</button>
            </div>
        </div>

        <div class="card" style="margin-top: 20px;">
            <h3>📋 Recent Decisions</h3>
            <div id="recentDecisions">Loading history...</div>
            <button class="btn btn-info" onclick="loadHistory()">View Full History</button>
        </div>

        <script>
            async function loadPendingRequests() {
                try {
                    const response = await fetch('/subadmin/requests/pending');
                    const data = await response.json();
                    
                    if (data.success) {
                        const requests = data.data.requests;
                        const container = document.getElementById('pendingRequests');
                        document.getElementById('pendingCount').textContent = requests.length;
                        
                        if (requests.length === 0) {
                            container.innerHTML = '<p>No pending requests</p>';
                            return;
                        }
                        
                        container.innerHTML = requests.map(req => `
                            <div class="request-item request-pending priority-${req.priority > 3 ? 'high' : req.priority > 1 ? 'medium' : 'low'}">
                                <h4>${req.visitor_name}</h4>
                                <p><strong>Purpose:</strong> ${req.visitor_purpose}</p>
                                <p><strong>Bot Summary:</strong> ${req.bot_conversation_summary}</p>
                                <p><strong>Bot Recommendation:</strong> ${req.bot_recommendation} (Confidence: ${(req.bot_confidence * 100).toFixed(1)}%)</p>
                                <p><strong>Requested:</strong> ${new Date(req.requested_at).toLocaleString()}</p>
                                <div>
                                    <button class="btn btn-approve" onclick="approveRequest('${req.id}')">✅ Approve</button>
                                    <button class="btn btn-deny" onclick="denyRequest('${req.id}')">❌ Deny</button>
                                    <button class="btn btn-info" onclick="viewDetails('${req.id}')">👁️ Details</button>
                                </div>
                            </div>
                        `).join('');
                    }
                } catch (error) {
                    console.error('Failed to load pending requests:', error);
                }
            }

            async function approveRequest(requestId) {
                const reason = prompt('Approval reason (optional):') || 'Approved by SubAdmin';
                
                try {
                    const response = await fetch(`/subadmin/requests/${requestId}/approve`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ reason })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert('Request approved successfully!');
                        refreshPanel();
                    } else {
                        alert('Failed to approve request');
                    }
                } catch (error) {
                    console.error('Failed to approve request:', error);
                    alert('Error approving request');
                }
            }

            async function denyRequest(requestId) {
                const reason = prompt('Denial reason:');
                if (!reason) return;
                
                try {
                    const response = await fetch(`/subadmin/requests/${requestId}/deny`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ reason })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        alert('Request denied successfully!');
                        refreshPanel();
                    } else {
                        alert('Failed to deny request');
                    }
                } catch (error) {
                    console.error('Failed to deny request:', error);
                    alert('Error denying request');
                }
            }

            async function loadAlerts() {
                try {
                    const response = await fetch('/subadmin/alerts');
                    const data = await response.json();
                    
                    if (data.success) {
                        const alerts = data.data.alerts;
                        const container = document.getElementById('systemAlerts');
                        document.getElementById('unreadAlerts').textContent = data.data.unread_count;
                        
                        container.innerHTML = alerts.slice(0, 5).map(alert => `
                            <div class="alert-item alert-${alert.severity}">
                                <strong>${alert.title}</strong><br>
                                <small>${alert.message}</small><br>
                                <small>${new Date(alert.created_at).toLocaleString()}</small>
                                ${!alert.acknowledged ? `<button class="btn btn-info" onclick="acknowledgeAlert('${alert.id}')">✓ Ack</button>` : ''}
                            </div>
                        `).join('');
                    }
                } catch (error) {
                    console.error('Failed to load alerts:', error);
                }
            }

            async function acknowledgeAlert(alertId) {
                try {
                    const response = await fetch(`/subadmin/alerts/${alertId}/acknowledge`, {
                        method: 'POST'
                    });
                    
                    if (response.ok) {
                        loadAlerts();
                    }
                } catch (error) {
                    console.error('Failed to acknowledge alert:', error);
                }
            }

            async function loadHistory() {
                try {
                    const response = await fetch('/subadmin/requests/history?limit=10');
                    const data = await response.json();
                    
                    if (data.success) {
                        const requests = data.data.requests;
                        const container = document.getElementById('recentDecisions');
                        
                        container.innerHTML = requests.map(req => `
                            <div class="request-item request-${req.status}">
                                <strong>${req.visitor_name}</strong> - ${req.status.toUpperCase()}<br>
                                <small>${req.visitor_purpose}</small><br>
                                <small>Decided: ${req.reviewed_at ? new Date(req.reviewed_at).toLocaleString() : 'Pending'}</small>
                            </div>
                        `).join('');
                    }
                } catch (error) {
                    console.error('Failed to load history:', error);
                }
            }

            function viewDetails(requestId) {
                // Open detailed view (could be a modal or new page)
                alert(`Viewing details for request: ${requestId}`);
            }

            function refreshPanel() {
                loadPendingRequests();
                loadAlerts();
                loadHistory();
            }

            // Initial load
            refreshPanel();
            
            // Auto-refresh every 15 seconds
            setInterval(refreshPanel, 15000);
        </script>
    </body>
    </html>
    """
