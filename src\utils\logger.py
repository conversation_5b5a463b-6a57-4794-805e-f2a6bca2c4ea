"""
Logging configuration for HISS
Provides structured logging with different levels and outputs
"""

import logging
import structlog
from pathlib import Path
from typing import Any, Dict
from .config import get_settings

settings = get_settings()


def configure_logging():
    """Configure structured logging for the application"""
    
    # Ensure log directory exists
    log_path = Path(settings.log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure standard logging
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        handlers=[
            logging.FileHandler(settings.log_file),
            logging.StreamHandler()
        ]
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger instance"""
    return structlog.get_logger(name)


def log_security_event(
    event_type: str,
    visitor_id: str = None,
    decision: str = None,
    reason: str = None,
    additional_data: Dict[str, Any] = None
):
    """Log security-related events with structured data"""
    logger = get_logger("security")
    
    event_data = {
        "event_type": event_type,
        "visitor_id": visitor_id,
        "decision": decision,
        "reason": reason,
        "system": settings.system_name
    }
    
    if additional_data:
        event_data.update(additional_data)
    
    logger.info("Security event", **event_data)


def log_interaction(
    interaction_type: str,
    visitor_id: str = None,
    message: str = None,
    response: str = None,
    additional_data: Dict[str, Any] = None
):
    """Log visitor interactions"""
    logger = get_logger("interaction")
    
    interaction_data = {
        "interaction_type": interaction_type,
        "visitor_id": visitor_id,
        "message": message,
        "response": response,
        "system": settings.system_name
    }
    
    if additional_data:
        interaction_data.update(additional_data)
    
    logger.info("Visitor interaction", **interaction_data)


# Initialize logging on import
configure_logging()
