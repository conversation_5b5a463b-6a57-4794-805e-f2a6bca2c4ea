"""
Google Gemini LLM integration for HISS
Handles LLM interactions with proper error handling and response formatting
"""

import asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.callbacks.base import BaseCallbackHandler

from ..utils.config import get_settings
from ..utils.logger import get_logger

settings = get_settings()
logger = get_logger(__name__)


class HissCallbackHandler(BaseCallbackHandler):
    """Custom callback handler for HISS LLM interactions"""
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        logger.info("LLM interaction started", prompts_count=len(prompts))
    
    def on_llm_end(self, response, **kwargs) -> None:
        logger.info("LLM interaction completed")
    
    def on_llm_error(self, error: Exception, **kwargs) -> None:
        logger.error(f"LLM interaction failed: {error}")


class HissLLM:
    """HISS LLM wrapper for Google Gemini"""
    
    def __init__(self):
        self.settings = settings
        self.callback_handler = HissCallbackHandler()
        self.llm = self._initialize_llm()
    
    def _initialize_llm(self) -> ChatGoogleGenerativeAI:
        """Initialize the Gemini LLM"""
        try:
            llm = ChatGoogleGenerativeAI(
                model=settings.model_name,
                google_api_key=settings.google_api_key,
                temperature=settings.model_temperature,
                max_tokens=settings.max_tokens,
                callbacks=[self.callback_handler]
            )
            
            logger.info(f"Initialized Gemini LLM: {settings.model_name}")
            return llm
            
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            raise
    
    def generate_response(
        self, 
        prompt: str, 
        system_message: str = None,
        context: List[str] = None
    ) -> str:
        """Generate a response using the LLM"""
        try:
            messages = []
            
            # Add system message if provided
            if system_message:
                messages.append(SystemMessage(content=system_message))
            
            # Add context if provided
            if context:
                context_text = "\n\n".join(context)
                context_msg = f"Relevant context:\n{context_text}\n\nUser query: {prompt}"
                messages.append(HumanMessage(content=context_msg))
            else:
                messages.append(HumanMessage(content=prompt))
            
            # Generate response
            response = self.llm.invoke(messages)
            
            logger.info("Generated LLM response", prompt_length=len(prompt))
            return response.content
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            raise
    
    async def generate_response_async(
        self, 
        prompt: str, 
        system_message: str = None,
        context: List[str] = None
    ) -> str:
        """Generate a response asynchronously"""
        try:
            messages = []
            
            if system_message:
                messages.append(SystemMessage(content=system_message))
            
            if context:
                context_text = "\n\n".join(context)
                context_msg = f"Relevant context:\n{context_text}\n\nUser query: {prompt}"
                messages.append(HumanMessage(content=context_msg))
            else:
                messages.append(HumanMessage(content=prompt))
            
            response = await self.llm.ainvoke(messages)
            
            logger.info("Generated async LLM response", prompt_length=len(prompt))
            return response.content
            
        except Exception as e:
            logger.error(f"Failed to generate async response: {e}")
            raise
    
    def generate_security_decision(
        self, 
        visitor_info: Dict[str, Any],
        context: List[str] = None,
        system_prompt: str = None
    ) -> Dict[str, Any]:
        """Generate a security decision for a visitor"""
        try:
            # Format visitor information
            visitor_text = self._format_visitor_info(visitor_info)
            
            # Create prompt for security decision
            prompt = f"""
            Please analyze the following visitor information and make a security decision:
            
            {visitor_text}
            
            Based on the security protocols and guidelines, provide:
            1. Decision: ALLOW or DENY
            2. Confidence: 0.0 to 1.0
            3. Reason: Brief explanation
            4. Recommendations: Any additional actions needed
            
            Respond in a structured format.
            """
            
            response = self.generate_response(
                prompt=prompt,
                system_message=system_prompt,
                context=context
            )
            
            # Parse the response (simplified - in production, use more robust parsing)
            decision_data = self._parse_security_decision(response)
            
            logger.info("Generated security decision", 
                       visitor_id=visitor_info.get('id'),
                       decision=decision_data.get('decision'))
            
            return decision_data
            
        except Exception as e:
            logger.error(f"Failed to generate security decision: {e}")
            raise
    
    def generate_conversation_response(
        self,
        message: str,
        conversation_history: List[Dict[str, str]] = None,
        visitor_context: Dict[str, Any] = None,
        system_prompt: str = None
    ) -> str:
        """Generate a conversational response for visitor interaction"""
        try:
            # Build conversation context
            context_parts = []
            
            if visitor_context:
                context_parts.append(f"Visitor context: {self._format_visitor_info(visitor_context)}")
            
            if conversation_history:
                history_text = self._format_conversation_history(conversation_history)
                context_parts.append(f"Conversation history:\n{history_text}")
            
            context_parts.append(f"Current message: {message}")
            
            full_context = "\n\n".join(context_parts)
            
            response = self.generate_response(
                prompt=full_context,
                system_message=system_prompt
            )
            
            logger.info("Generated conversation response", 
                       message_length=len(message))
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to generate conversation response: {e}")
            raise
    
    def _format_visitor_info(self, visitor_info: Dict[str, Any]) -> str:
        """Format visitor information for LLM consumption"""
        formatted_parts = []
        for key, value in visitor_info.items():
            if value is not None:
                formatted_parts.append(f"{key.replace('_', ' ').title()}: {value}")
        return "\n".join(formatted_parts)
    
    def _format_conversation_history(self, history: List[Dict[str, str]]) -> str:
        """Format conversation history for LLM consumption"""
        formatted_history = []
        for entry in history[-5:]:  # Last 5 exchanges
            role = entry.get('role', 'unknown')
            content = entry.get('content', '')
            formatted_history.append(f"{role.title()}: {content}")
        return "\n".join(formatted_history)
    
    def _parse_security_decision(self, response: str) -> Dict[str, Any]:
        """Parse security decision from LLM response"""
        # Simplified parsing - in production, use more robust methods
        decision_data = {
            'decision': 'DENY',  # Default to deny for safety
            'confidence': 0.5,
            'reason': 'Unable to parse decision',
            'recommendations': []
        }
        
        try:
            lines = response.strip().split('\n')
            for line in lines:
                line = line.strip()
                if 'decision:' in line.lower():
                    if 'allow' in line.lower():
                        decision_data['decision'] = 'ALLOW'
                    elif 'deny' in line.lower():
                        decision_data['decision'] = 'DENY'
                elif 'confidence:' in line.lower():
                    try:
                        conf_str = line.split(':')[1].strip()
                        decision_data['confidence'] = float(conf_str)
                    except:
                        pass
                elif 'reason:' in line.lower():
                    decision_data['reason'] = line.split(':', 1)[1].strip()
        except Exception as e:
            logger.warning(f"Failed to parse security decision: {e}")
        
        return decision_data


# Global LLM instance
_llm_instance = None


def get_llm() -> HissLLM:
    """Get the global LLM instance"""
    global _llm_instance
    if _llm_instance is None:
        _llm_instance = HissLLM()
    return _llm_instance
