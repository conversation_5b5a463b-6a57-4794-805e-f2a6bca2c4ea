# HISS API Documentation

## Overview

The HISS (Human Interactive Security System) provides a comprehensive REST API for managing security operations, visitor interactions, and system administration.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, the API does not require authentication for development purposes. In production, implement proper authentication mechanisms.

## Endpoints

### System Status

#### GET /
Get basic system information.

**Response:**
```json
{
  "success": true,
  "message": "Welcome to HISS Security Guard",
  "data": {
    "version": "1.0.0",
    "status": "online",
    "timestamp": "2024-01-01T12:00:00"
  }
}
```

#### GET /health
Get detailed system health status.

**Response:**
```json
{
  "status": "online",
  "security_level": "high",
  "active_visitors": 5,
  "pending_decisions": 2,
  "active_alerts": 0,
  "system_version": "1.0.0"
}
```

### Visitor Management

#### POST /visitors/register
Register a new visitor.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "purpose": "Business meeting",
  "company": "TechCorp",
  "contact_email": "<EMAIL>",
  "contact_phone": "******-0123",
  "appointment_with": "<PERSON>",
  "expected_duration": "2 hours"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Visitor registered successfully",
  "data": {
    "visitor_id": "uuid-string",
    "status": "registered"
  }
}
```

#### POST /visitors/{visitor_id}/screen
Screen a visitor for access decision.

**Response:**
```json
{
  "success": true,
  "message": "Visitor screening completed",
  "data": {
    "decision": "ALLOW",
    "confidence": 0.85,
    "reason": "Valid business purpose and proper identification",
    "decision_id": "decision-uuid",
    "messages": [...]
  }
}
```

#### GET /visitors
List all visitors with optional status filter.

**Query Parameters:**
- `status` (optional): Filter by visitor status

**Response:**
```json
[
  {
    "id": "visitor-uuid",
    "name": "John Doe",
    "status": "checked_in",
    "purpose": "Business meeting",
    "created_at": "2024-01-01T10:00:00"
  }
]
```

#### GET /visitors/{visitor_id}
Get specific visitor information.

### Chat Interface

#### POST /chat
Send a message to the security system.

**Request Body:**
```json
{
  "visitor_id": "visitor-uuid",
  "message": "Can you help me find the conference room?",
  "context": {
    "location": "lobby"
  }
}
```

**Response:**
```json
{
  "message_id": "message-uuid",
  "response": "I'd be happy to help you find the conference room...",
  "timestamp": "2024-01-01T12:00:00",
  "suggestions": [
    "Can you provide a map?",
    "What floor is it on?",
    "Who should I contact?"
  ]
}
```

#### GET /chat
Get the interactive chat interface (HTML page).

#### WebSocket /ws/chat
Real-time chat via WebSocket connection.

**Message Format:**
```json
{
  "visitor_id": "visitor-uuid",
  "message": "Hello, I need assistance"
}
```

### Security Decisions

#### GET /decisions
List all security decisions.

#### GET /decisions/{decision_id}
Get specific security decision details.

### Conversations

#### GET /conversations/{visitor_id}
Get conversation history for a visitor.

### System Management

#### POST /system/reset
Reset the system (development only).

#### GET /knowledge/info
Get information about the knowledge base.

**Response:**
```json
{
  "success": true,
  "message": "Knowledge base information retrieved",
  "data": {
    "name": "hiss_security_knowledge",
    "document_count": 25,
    "embedding_model": "models/embedding-001"
  }
}
```

## Error Responses

All endpoints return error responses in the following format:

```json
{
  "detail": "Error description"
}
```

Common HTTP status codes:
- `400`: Bad Request - Invalid input data
- `404`: Not Found - Resource not found
- `500`: Internal Server Error - System error

## Rate Limiting

Currently no rate limiting is implemented. Consider adding rate limiting for production use.

## WebSocket Events

The WebSocket chat interface supports real-time bidirectional communication:

1. **Connection**: Connect to `/ws/chat` with optional `visitor_id` parameter
2. **Send Message**: Send JSON message with `message` and optional `visitor_id`
3. **Receive Response**: Receive JSON response with AI-generated reply and suggestions

## Examples

See the `examples/` directory for complete usage examples in various programming languages.
