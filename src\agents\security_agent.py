"""
Langgraph Security Agent for HISS
Implements the main agentic workflow for security decision-making
"""

from typing import Dict, Any, List, Optional, TypedDict, Annotated
from datetime import datetime
import operator

from langgraph.graph import StateGraph, END

from langchain.tools import BaseTool
from langchain.schema import Document

from ..models.security import (
    VisitorInfo, SecurityDecision, AccessDecision, 
    SecurityLevel, ConversationMessage, SystemResponse
)
from ..database.vector_store import get_vector_store
from ..utils.llm import get_llm
from ..prompts.templates import get_prompt_templates
from ..utils.logger import get_logger, log_security_event, log_interaction

logger = get_logger(__name__)


class SecurityAgentState(TypedDict):
    """State for the security agent workflow"""
    visitor_info: Dict[str, Any]
    messages: Annotated[List[ConversationMessage], operator.add]
    security_decision: Optional[Dict[str, Any]]
    relevant_documents: List[Document]
    current_step: str
    context: Dict[str, Any]
    error: Optional[str]
    requires_human_intervention: bool


class SecurityKnowledgeTool(BaseTool):
    """Tool for retrieving security knowledge from vector store"""
    
    name: str = "security_knowledge"
    description: str = "Retrieve relevant security protocols and guidelines"
    
    def __init__(self):
        super().__init__()
        self.vector_store = get_vector_store()
    
    def _run(self, query: str, context_type: str = None) -> List[Document]:
        """Retrieve relevant security knowledge"""
        try:
            documents = self.vector_store.get_relevant_documents(
                query=query,
                context_type=context_type,
                max_docs=5
            )
            return documents
        except Exception as e:
            logger.error(f"Failed to retrieve security knowledge: {e}")
            return []
    
    async def _arun(self, query: str, context_type: str = None) -> List[Document]:
        """Async version of knowledge retrieval"""
        return self._run(query, context_type)


class VisitorScreeningTool(BaseTool):
    """Tool for screening visitors based on security protocols"""
    
    name: str = "visitor_screening"
    description: str = "Screen visitors and make access decisions"
    
    def __init__(self):
        super().__init__()
        self.llm = get_llm()
        self.prompts = get_prompt_templates()
    
    def _run(self, visitor_info: Dict[str, Any], security_context: List[Document]) -> Dict[str, Any]:
        """Screen a visitor and make security decision"""
        try:
            # Format security context
            context_text = "\n\n".join([doc.page_content for doc in security_context])
            
            # Format visitor information
            visitor_text = self.prompts.format_visitor_info(visitor_info)
            
            # Generate security decision
            decision_data = self.llm.generate_security_decision(
                visitor_info=visitor_info,
                context=[context_text],
                system_prompt=self.prompts.security_guard_system_prompt
            )
            
            return decision_data
            
        except Exception as e:
            logger.error(f"Visitor screening failed: {e}")
            return {
                'decision': 'DENY',
                'confidence': 0.0,
                'reason': f'Screening failed: {str(e)}',
                'recommendations': ['Manual review required']
            }
    
    async def _arun(self, visitor_info: Dict[str, Any], security_context: List[Document]) -> Dict[str, Any]:
        """Async version of visitor screening"""
        return self._run(visitor_info, security_context)


class HissSecurityAgent:
    """Main security agent using Langgraph workflow"""
    
    def __init__(self):
        self.vector_store = get_vector_store()
        self.llm = get_llm()
        self.prompts = get_prompt_templates()
        
        # Initialize tools
        self.knowledge_tool = SecurityKnowledgeTool()
        self.screening_tool = VisitorScreeningTool()
        
        # Build the workflow graph
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """Build the Langgraph workflow"""
        workflow = StateGraph(SecurityAgentState)
        
        # Add nodes
        workflow.add_node("analyze_visitor", self._analyze_visitor)
        workflow.add_node("retrieve_knowledge", self._retrieve_knowledge)
        workflow.add_node("make_decision", self._make_decision)
        workflow.add_node("generate_response", self._generate_response)
        workflow.add_node("handle_conversation", self._handle_conversation)
        workflow.add_node("emergency_response", self._emergency_response)
        
        # Add edges
        workflow.add_edge("analyze_visitor", "retrieve_knowledge")
        workflow.add_edge("retrieve_knowledge", "make_decision")
        workflow.add_conditional_edges(
            "make_decision",
            self._decision_router,
            {
                "approved": "generate_response",
                "denied": "generate_response",
                "conversation": "handle_conversation",
                "emergency": "emergency_response"
            }
        )
        workflow.add_edge("generate_response", END)
        workflow.add_edge("handle_conversation", END)
        workflow.add_edge("emergency_response", END)
        
        # Set entry point
        workflow.set_entry_point("analyze_visitor")
        
        return workflow.compile()
    
    def _analyze_visitor(self, state: SecurityAgentState) -> SecurityAgentState:
        """Analyze visitor information and determine next steps"""
        try:
            visitor_info = state["visitor_info"]
            
            # Basic validation
            if not visitor_info.get("name") or not visitor_info.get("purpose"):
                state["error"] = "Incomplete visitor information"
                state["requires_human_intervention"] = True
                return state
            
            # Determine security level based on purpose and other factors
            purpose = visitor_info.get("purpose", "").lower()
            if any(keyword in purpose for keyword in ["emergency", "urgent", "critical"]):
                state["context"]["security_level"] = SecurityLevel.HIGH
            elif visitor_info.get("company"):
                state["context"]["security_level"] = SecurityLevel.MEDIUM
            else:
                state["context"]["security_level"] = SecurityLevel.MEDIUM
            
            state["current_step"] = "visitor_analyzed"
            logger.info(f"Analyzed visitor: {visitor_info.get('name')}")
            
        except Exception as e:
            logger.error(f"Visitor analysis failed: {e}")
            state["error"] = str(e)
        
        return state
    
    def _retrieve_knowledge(self, state: SecurityAgentState) -> SecurityAgentState:
        """Retrieve relevant security knowledge"""
        try:
            visitor_info = state["visitor_info"]
            
            # Build query for knowledge retrieval
            query_parts = [
                visitor_info.get("purpose", ""),
                visitor_info.get("company", ""),
                "security protocol visitor access"
            ]
            query = " ".join(filter(None, query_parts))
            
            # Retrieve relevant documents
            documents = self.knowledge_tool._run(query, "security_protocol")
            state["relevant_documents"] = documents
            
            state["current_step"] = "knowledge_retrieved"
            logger.info(f"Retrieved {len(documents)} relevant documents")
            
        except Exception as e:
            logger.error(f"Knowledge retrieval failed: {e}")
            state["error"] = str(e)
        
        return state
    
    def _make_decision(self, state: SecurityAgentState) -> SecurityAgentState:
        """Make security decision based on analysis and knowledge"""
        try:
            visitor_info = state["visitor_info"]
            relevant_docs = state["relevant_documents"]
            
            # Use screening tool to make decision
            decision_data = self.screening_tool._run(visitor_info, relevant_docs)
            
            # Create security decision object
            security_decision = SecurityDecision(
                visitor_id=visitor_info.get("id", "unknown"),
                decision=AccessDecision(decision_data["decision"]),
                confidence=decision_data["confidence"],
                reason=decision_data["reason"],
                conditions=decision_data.get("conditions", []),
                recommendations=decision_data.get("recommendations", []),
                security_level=state["context"].get("security_level", SecurityLevel.MEDIUM)
            )
            
            state["security_decision"] = security_decision.dict()
            state["current_step"] = "decision_made"
            
            # Log security event
            log_security_event(
                event_type="access_decision",
                visitor_id=visitor_info.get("id"),
                decision=decision_data["decision"],
                reason=decision_data["reason"]
            )
            
            logger.info(f"Security decision made: {decision_data['decision']} for {visitor_info.get('name')}")
            
        except Exception as e:
            logger.error(f"Decision making failed: {e}")
            state["error"] = str(e)
        
        return state
    
    def _generate_response(self, state: SecurityAgentState) -> SecurityAgentState:
        """Generate appropriate response based on decision"""
        try:
            decision = state["security_decision"]
            visitor_info = state["visitor_info"]
            
            if decision["decision"] == "ALLOW":
                # Generate welcome message
                prompt = self.prompts.visitor_welcome_prompt
                response_text = self.llm.generate_response(
                    prompt=prompt.format(
                        visitor_info=self.prompts.format_visitor_info(visitor_info),
                        access_details=f"Access granted with confidence {decision['confidence']:.2f}",
                        facility_info="Please follow all security guidelines during your visit."
                    ),
                    system_message=self.prompts.security_guard_system_prompt
                )
            else:
                # Generate denial message
                prompt = self.prompts.access_denied_prompt
                response_text = self.llm.generate_response(
                    prompt=prompt.format(
                        visitor_info=self.prompts.format_visitor_info(visitor_info),
                        denial_reason=decision["reason"],
                        alternative_options="Please contact the person you're visiting or security for assistance."
                    ),
                    system_message=self.prompts.security_guard_system_prompt
                )
            
            # Create response message
            response_message = ConversationMessage(
                visitor_id=visitor_info.get("id"),
                role="security_guard",
                content=response_text,
                message_type="decision_response"
            )
            
            state["messages"].append(response_message)
            state["current_step"] = "response_generated"
            
        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            state["error"] = str(e)
        
        return state
    
    def _handle_conversation(self, state: SecurityAgentState) -> SecurityAgentState:
        """Handle ongoing conversation with visitor"""
        try:
            # This would handle follow-up questions or conversations
            # Implementation depends on specific conversation flow needs
            state["current_step"] = "conversation_handled"
            
        except Exception as e:
            logger.error(f"Conversation handling failed: {e}")
            state["error"] = str(e)
        
        return state
    
    def _emergency_response(self, state: SecurityAgentState) -> SecurityAgentState:
        """Handle emergency situations"""
        try:
            # Emergency response logic
            state["current_step"] = "emergency_handled"
            state["requires_human_intervention"] = True
            
        except Exception as e:
            logger.error(f"Emergency response failed: {e}")
            state["error"] = str(e)
        
        return state
    
    def _decision_router(self, state: SecurityAgentState) -> str:
        """Route based on decision type"""
        if state.get("error"):
            return "emergency"
        
        decision = state.get("security_decision")
        if not decision:
            return "emergency"
        
        if decision["decision"] == "ALLOW":
            return "approved"
        elif decision["decision"] == "DENY":
            return "denied"
        else:
            return "conversation"
    
    async def process_visitor(self, visitor_info: Dict[str, Any]) -> Dict[str, Any]:
        """Process a visitor through the security workflow"""
        try:
            # Initialize state
            initial_state = SecurityAgentState(
                visitor_info=visitor_info,
                messages=[],
                security_decision=None,
                relevant_documents=[],
                current_step="initialized",
                context={},
                error=None,
                requires_human_intervention=False
            )
            
            # Run the workflow
            final_state = await self.workflow.ainvoke(initial_state)
            
            return {
                "success": final_state.get("error") is None,
                "security_decision": final_state.get("security_decision"),
                "messages": [msg.dict() for msg in final_state.get("messages", [])],
                "requires_human_intervention": final_state.get("requires_human_intervention", False),
                "error": final_state.get("error")
            }
            
        except Exception as e:
            logger.error(f"Visitor processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "requires_human_intervention": True
            }


# Global security agent instance
_security_agent = None


def get_security_agent() -> HissSecurityAgent:
    """Get the global security agent instance"""
    global _security_agent
    if _security_agent is None:
        _security_agent = HissSecurityAgent()
    return _security_agent
