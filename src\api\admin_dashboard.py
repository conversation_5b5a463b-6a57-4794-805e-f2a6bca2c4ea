"""
Admin Dashboard for HISS
Provides admin interface for viewing conversations and managing system
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import HTMLResponse
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..models.admin import User, UserRole, PermissionType, AdminAction, ConversationSession
from ..models.security import ConversationMessage, VisitorInfo
from ..utils.logger import get_logger

logger = get_logger(__name__)

# Global storage (in production, use proper database)
admin_users_db: Dict[str, User] = {}
conversation_sessions_db: Dict[str, ConversationSession] = {}
admin_actions_db: List[AdminAction] = []

router = APIRouter(prefix="/admin", tags=["admin"])


def get_current_admin(user_id: str = None) -> User:
    """Get current admin user (simplified auth)"""
    if not user_id or user_id not in admin_users_db:
        raise HTTPException(status_code=401, detail="Admin authentication required")
    
    user = admin_users_db[user_id]
    if user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin privileges required")
    
    return user


def log_admin_action(admin: User, action_type: str, description: str, target_id: str = None, target_type: str = None):
    """Log admin action"""
    action = AdminAction(
        admin_id=admin.id,
        admin_name=admin.full_name,
        action_type=action_type,
        target_id=target_id,
        target_type=target_type,
        description=description
    )
    admin_actions_db.append(action)


@router.get("/dashboard", response_class=HTMLResponse)
async def get_admin_dashboard():
    """Get admin dashboard HTML"""
    return get_admin_dashboard_html()


@router.get("/conversations/all")
async def get_all_conversations(
    admin: User = Depends(get_current_admin),
    limit: int = Query(50, le=200),
    offset: int = Query(0, ge=0),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None)
):
    """Get all conversation history (admin only)"""
    
    # Check permission
    if not admin.has_permission(PermissionType.VIEW_ALL_CONVERSATIONS):
        raise HTTPException(status_code=403, detail="Permission denied")
    
    # Log admin action
    log_admin_action(admin, "view_conversations", f"Viewed conversations (limit: {limit})")
    
    try:
        # Get conversations from main app storage
        from ..api.main import conversations_db
        
        all_conversations = []
        for visitor_id, messages in conversations_db.items():
            for message in messages:
                conversation_data = {
                    "visitor_id": visitor_id,
                    "message_id": message.id,
                    "role": message.role,
                    "content": message.content,
                    "timestamp": message.timestamp,
                    "message_type": message.message_type,
                    "location": getattr(message, 'location', 'unknown'),
                    "metadata": message.metadata
                }
                all_conversations.append(conversation_data)
        
        # Sort by timestamp
        all_conversations.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # Apply date filters if provided
        if date_from:
            date_from_dt = datetime.fromisoformat(date_from)
            all_conversations = [c for c in all_conversations if c['timestamp'] >= date_from_dt]
        
        if date_to:
            date_to_dt = datetime.fromisoformat(date_to)
            all_conversations = [c for c in all_conversations if c['timestamp'] <= date_to_dt]
        
        # Apply pagination
        total = len(all_conversations)
        conversations = all_conversations[offset:offset + limit]
        
        return {
            "success": True,
            "data": {
                "conversations": conversations,
                "total": total,
                "limit": limit,
                "offset": offset
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get conversations: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve conversations")


@router.get("/conversations/sessions")
async def get_conversation_sessions(
    admin: User = Depends(get_current_admin),
    limit: int = Query(20, le=100)
):
    """Get conversation sessions summary"""
    
    if not admin.has_permission(PermissionType.VIEW_ALL_CONVERSATIONS):
        raise HTTPException(status_code=403, detail="Permission denied")
    
    log_admin_action(admin, "view_sessions", "Viewed conversation sessions")
    
    try:
        sessions = list(conversation_sessions_db.values())
        sessions.sort(key=lambda x: x.started_at, reverse=True)
        
        return {
            "success": True,
            "data": {
                "sessions": sessions[:limit],
                "total": len(sessions)
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve sessions")


@router.get("/analytics/summary")
async def get_analytics_summary(
    admin: User = Depends(get_current_admin),
    days: int = Query(7, ge=1, le=30)
):
    """Get analytics summary for admin dashboard"""
    
    if not admin.has_permission(PermissionType.VIEW_ANALYTICS):
        raise HTTPException(status_code=403, detail="Permission denied")
    
    log_admin_action(admin, "view_analytics", f"Viewed analytics for {days} days")
    
    try:
        # Get data from main app
        from ..api.main import visitors_db, decisions_db, conversations_db
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Visitor statistics
        total_visitors = len(visitors_db)
        recent_visitors = [v for v in visitors_db.values() if v.created_at >= start_date]
        
        # Decision statistics
        total_decisions = len(decisions_db)
        recent_decisions = [d for d in decisions_db.values() if d.decided_at >= start_date]
        approved_decisions = [d for d in recent_decisions if d.decision == "ALLOW"]
        denied_decisions = [d for d in recent_decisions if d.decision == "DENY"]
        
        # Conversation statistics
        total_conversations = sum(len(msgs) for msgs in conversations_db.values())
        
        # Session statistics
        active_sessions = [s for s in conversation_sessions_db.values() if s.ended_at is None]
        
        analytics = {
            "period_days": days,
            "visitors": {
                "total": total_visitors,
                "recent": len(recent_visitors),
                "by_status": {}
            },
            "decisions": {
                "total": total_decisions,
                "recent": len(recent_decisions),
                "approved": len(approved_decisions),
                "denied": len(denied_decisions),
                "approval_rate": len(approved_decisions) / max(len(recent_decisions), 1) * 100
            },
            "conversations": {
                "total_messages": total_conversations,
                "active_sessions": len(active_sessions),
                "total_sessions": len(conversation_sessions_db)
            },
            "system": {
                "uptime": "N/A",  # Would calculate from startup time
                "last_updated": datetime.now()
            }
        }
        
        # Visitor status breakdown
        for visitor in visitors_db.values():
            status = visitor.status
            analytics["visitors"]["by_status"][status] = analytics["visitors"]["by_status"].get(status, 0) + 1
        
        return {
            "success": True,
            "data": analytics
        }
        
    except Exception as e:
        logger.error(f"Failed to get analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve analytics")


@router.get("/actions/history")
async def get_admin_actions(
    admin: User = Depends(get_current_admin),
    limit: int = Query(50, le=200)
):
    """Get admin action history"""
    
    log_admin_action(admin, "view_action_history", "Viewed admin action history")
    
    try:
        actions = sorted(admin_actions_db, key=lambda x: x.timestamp, reverse=True)
        
        return {
            "success": True,
            "data": {
                "actions": actions[:limit],
                "total": len(actions)
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get admin actions: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve admin actions")


@router.get("/users/list")
async def get_users(
    admin: User = Depends(get_current_admin)
):
    """Get all users (admin only)"""
    
    if not admin.has_permission(PermissionType.MANAGE_USERS):
        raise HTTPException(status_code=403, detail="Permission denied")
    
    log_admin_action(admin, "view_users", "Viewed user list")
    
    try:
        users = []
        for user in admin_users_db.values():
            user_data = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "role": user.role,
                "is_active": user.is_active,
                "created_at": user.created_at,
                "last_login": user.last_login,
                "department": user.department
            }
            users.append(user_data)
        
        return {
            "success": True,
            "data": {"users": users}
        }
        
    except Exception as e:
        logger.error(f"Failed to get users: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve users")


def get_admin_dashboard_html() -> str:
    """Generate admin dashboard HTML"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>HISS Admin Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .header { background: #2c3e50; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
            .dashboard-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
            .card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .stat-number { font-size: 2em; font-weight: bold; color: #3498db; }
            .stat-label { color: #7f8c8d; margin-top: 5px; }
            .conversation-list { max-height: 400px; overflow-y: auto; }
            .conversation-item { border-bottom: 1px solid #eee; padding: 10px 0; }
            .conversation-meta { font-size: 0.9em; color: #7f8c8d; }
            .role-visitor { color: #e74c3c; }
            .role-system { color: #27ae60; }
            .btn { padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            .btn:hover { background: #2980b9; }
            .refresh-btn { float: right; }
            .analytics-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin: 20px 0; }
            .metric-card { background: white; padding: 15px; border-radius: 8px; text-align: center; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🛡️ HISS Admin Dashboard</h1>
            <p>Human Interactive Security System - Administrative Control Panel</p>
            <button class="btn refresh-btn" onclick="refreshDashboard()">🔄 Refresh</button>
        </div>

        <div class="analytics-grid">
            <div class="metric-card">
                <div class="stat-number" id="totalVisitors">-</div>
                <div class="stat-label">Total Visitors</div>
            </div>
            <div class="metric-card">
                <div class="stat-number" id="activeConversations">-</div>
                <div class="stat-label">Active Conversations</div>
            </div>
            <div class="metric-card">
                <div class="stat-number" id="approvalRate">-</div>
                <div class="stat-label">Approval Rate</div>
            </div>
            <div class="metric-card">
                <div class="stat-number" id="totalDecisions">-</div>
                <div class="stat-label">Total Decisions</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <h3>📊 System Analytics</h3>
                <div id="analyticsContent">Loading analytics...</div>
                <button class="btn" onclick="loadAnalytics()">Refresh Analytics</button>
            </div>

            <div class="card">
                <h3>👥 Recent Admin Actions</h3>
                <div id="adminActions">Loading actions...</div>
                <button class="btn" onclick="loadAdminActions()">View All Actions</button>
            </div>
        </div>

        <div class="card">
            <h3>💬 Recent Conversations</h3>
            <div class="conversation-list" id="conversationList">
                Loading conversations...
            </div>
            <button class="btn" onclick="loadConversations()">Load More</button>
            <button class="btn" onclick="exportConversations()">📥 Export</button>
        </div>

        <script>
            let currentOffset = 0;
            const limit = 20;

            async function loadAnalytics() {
                try {
                    const response = await fetch('/admin/analytics/summary?days=7');
                    const data = await response.json();
                    
                    if (data.success) {
                        const analytics = data.data;
                        document.getElementById('totalVisitors').textContent = analytics.visitors.total;
                        document.getElementById('activeConversations').textContent = analytics.conversations.active_sessions;
                        document.getElementById('approvalRate').textContent = analytics.decisions.approval_rate.toFixed(1) + '%';
                        document.getElementById('totalDecisions').textContent = analytics.decisions.total;
                        
                        document.getElementById('analyticsContent').innerHTML = `
                            <p><strong>Recent Visitors:</strong> ${analytics.visitors.recent}</p>
                            <p><strong>Approved:</strong> ${analytics.decisions.approved}</p>
                            <p><strong>Denied:</strong> ${analytics.decisions.denied}</p>
                            <p><strong>Total Messages:</strong> ${analytics.conversations.total_messages}</p>
                        `;
                    }
                } catch (error) {
                    console.error('Failed to load analytics:', error);
                }
            }

            async function loadConversations() {
                try {
                    const response = await fetch(`/admin/conversations/all?limit=${limit}&offset=${currentOffset}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        const conversations = data.data.conversations;
                        const listElement = document.getElementById('conversationList');
                        
                        if (currentOffset === 0) {
                            listElement.innerHTML = '';
                        }
                        
                        conversations.forEach(conv => {
                            const item = document.createElement('div');
                            item.className = 'conversation-item';
                            item.innerHTML = `
                                <div><strong class="role-${conv.role}">${conv.role}:</strong> ${conv.content.substring(0, 100)}${conv.content.length > 100 ? '...' : ''}</div>
                                <div class="conversation-meta">
                                    Visitor: ${conv.visitor_id || 'Anonymous'} | 
                                    Time: ${new Date(conv.timestamp).toLocaleString()} |
                                    Location: ${conv.location}
                                </div>
                            `;
                            listElement.appendChild(item);
                        });
                        
                        currentOffset += conversations.length;
                    }
                } catch (error) {
                    console.error('Failed to load conversations:', error);
                }
            }

            async function loadAdminActions() {
                try {
                    const response = await fetch('/admin/actions/history?limit=10');
                    const data = await response.json();
                    
                    if (data.success) {
                        const actions = data.data.actions;
                        const actionsElement = document.getElementById('adminActions');
                        
                        actionsElement.innerHTML = actions.map(action => `
                            <div style="margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                                <strong>${action.admin_name}</strong> - ${action.action_type}<br>
                                <small>${action.description} (${new Date(action.timestamp).toLocaleString()})</small>
                            </div>
                        `).join('');
                    }
                } catch (error) {
                    console.error('Failed to load admin actions:', error);
                }
            }

            function refreshDashboard() {
                currentOffset = 0;
                loadAnalytics();
                loadConversations();
                loadAdminActions();
            }

            function exportConversations() {
                window.open('/admin/conversations/all?limit=1000&format=csv', '_blank');
            }

            // Load initial data
            refreshDashboard();
            
            // Auto-refresh every 30 seconds
            setInterval(refreshDashboard, 30000);
        </script>
    </body>
    </html>
    """


# Initialize default admin users
def initialize_admin_users():
    """Initialize default admin users"""
    from ..models.admin import create_default_admin, create_default_subadmin
    
    if not admin_users_db:
        admin = create_default_admin()
        subadmin = create_default_subadmin()
        
        admin_users_db[admin.id] = admin
        admin_users_db[subadmin.id] = subadmin
        
        logger.info("Default admin users initialized")


# Initialize on import
initialize_admin_users()
