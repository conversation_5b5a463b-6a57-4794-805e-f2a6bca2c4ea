"""
Interactive chat interface for HISS
Provides real-time chat functionality with WebSocket support
"""

from fastapi import WebSocket, WebSocketDisconnect, Depends
from fastapi.responses import HTM<PERSON><PERSON>ponse
from typing import List, Dict, Any
import json
import asyncio
from datetime import datetime

from ..models.security import ChatRe<PERSON>, ChatResponse, ConversationMessage
from ..utils.llm import get_llm
from ..prompts.templates import get_prompt_templates
from ..utils.logger import get_logger, log_interaction

logger = get_logger(__name__)


class ConnectionManager:
    """Manages WebSocket connections for real-time chat"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.visitor_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, visitor_id: str = None):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        if visitor_id:
            self.visitor_connections[visitor_id] = websocket
        
        logger.info(f"New WebSocket connection established for visitor: {visitor_id}")
    
    def disconnect(self, websocket: WebSocket, visitor_id: str = None):
        """Remove a WebSocket connection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        if visitor_id and visitor_id in self.visitor_connections:
            del self.visitor_connections[visitor_id]
        
        logger.info(f"WebSocket connection closed for visitor: {visitor_id}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
    
    async def send_to_visitor(self, visitor_id: str, message: str):
        """Send a message to a specific visitor"""
        if visitor_id in self.visitor_connections:
            await self.send_personal_message(message, self.visitor_connections[visitor_id])
    
    async def broadcast(self, message: str):
        """Broadcast a message to all connected clients"""
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Failed to broadcast message: {e}")


# Global connection manager
manager = ConnectionManager()


class HissChatInterface:
    """Interactive chat interface for HISS"""
    
    def __init__(self):
        self.llm = get_llm()
        self.prompts = get_prompt_templates()
        self.conversation_history: Dict[str, List[ConversationMessage]] = {}
    
    async def process_message(
        self, 
        visitor_id: str, 
        message: str, 
        visitor_context: Dict[str, Any] = None
    ) -> ChatResponse:
        """Process a chat message and generate response"""
        try:
            # Get conversation history
            history = self.conversation_history.get(visitor_id, [])
            
            # Generate response
            response_text = self.llm.generate_conversation_response(
                message=message,
                conversation_history=[msg.dict() for msg in history[-5:]],
                visitor_context=visitor_context,
                system_prompt=self.prompts.security_guard_system_prompt
            )
            
            # Create conversation messages
            user_message = ConversationMessage(
                visitor_id=visitor_id,
                role="visitor",
                content=message,
                message_type="chat"
            )
            
            system_message = ConversationMessage(
                visitor_id=visitor_id,
                role="security_guard",
                content=response_text,
                message_type="chat_response"
            )
            
            # Store conversation
            if visitor_id not in self.conversation_history:
                self.conversation_history[visitor_id] = []
            
            self.conversation_history[visitor_id].extend([user_message, system_message])
            
            # Log interaction
            log_interaction(
                interaction_type="chat",
                visitor_id=visitor_id,
                message=message,
                response=response_text
            )
            
            # Generate suggestions based on context
            suggestions = self._generate_suggestions(message, visitor_context)
            
            return ChatResponse(
                message_id=system_message.id,
                response=response_text,
                suggestions=suggestions,
                requires_action=self._check_requires_action(response_text)
            )
            
        except Exception as e:
            logger.error(f"Chat message processing failed: {e}")
            return ChatResponse(
                message_id="error",
                response="I apologize, but I'm experiencing technical difficulties. Please contact security directly for assistance.",
                suggestions=["Contact security desk", "Try again later"]
            )
    
    def _generate_suggestions(self, message: str, visitor_context: Dict[str, Any] = None) -> List[str]:
        """Generate contextual suggestions for the visitor"""
        suggestions = []
        
        message_lower = message.lower()
        
        if "direction" in message_lower or "where" in message_lower:
            suggestions.extend([
                "Can you provide a map?",
                "What floor is the meeting room on?",
                "Where is the nearest restroom?"
            ])
        elif "time" in message_lower or "when" in message_lower:
            suggestions.extend([
                "What are the visiting hours?",
                "When does the building close?",
                "How long will this take?"
            ])
        elif "help" in message_lower or "problem" in message_lower:
            suggestions.extend([
                "Can you contact my host?",
                "I need technical assistance",
                "Who should I speak with?"
            ])
        else:
            suggestions.extend([
                "Can you help me with directions?",
                "What are the building policies?",
                "How do I contact someone?"
            ])
        
        return suggestions[:3]  # Limit to 3 suggestions
    
    def _check_requires_action(self, response: str) -> bool:
        """Check if the response requires follow-up action"""
        action_keywords = [
            "contact security", "speak with", "call", "notify",
            "emergency", "urgent", "immediate", "escalate"
        ]
        
        response_lower = response.lower()
        return any(keyword in response_lower for keyword in action_keywords)
    
    def get_conversation_history(self, visitor_id: str) -> List[ConversationMessage]:
        """Get conversation history for a visitor"""
        return self.conversation_history.get(visitor_id, [])
    
    def clear_conversation(self, visitor_id: str):
        """Clear conversation history for a visitor"""
        if visitor_id in self.conversation_history:
            del self.conversation_history[visitor_id]


# Global chat interface
chat_interface = HissChatInterface()


def get_chat_html() -> str:
    """Generate HTML for the chat interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>HISS Security Chat</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .chat-container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .chat-header { background: #2c3e50; color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center; }
            .chat-messages { height: 400px; overflow-y: auto; padding: 20px; border-bottom: 1px solid #eee; }
            .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
            .user-message { background: #3498db; color: white; margin-left: 50px; }
            .system-message { background: #ecf0f1; color: #2c3e50; margin-right: 50px; }
            .chat-input { display: flex; padding: 20px; }
            .chat-input input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin-right: 10px; }
            .chat-input button { padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; }
            .suggestions { padding: 0 20px 20px; }
            .suggestion-btn { background: #ecf0f1; border: 1px solid #bdc3c7; padding: 5px 10px; margin: 5px; border-radius: 3px; cursor: pointer; display: inline-block; }
            .status { padding: 10px; text-align: center; color: #7f8c8d; }
        </style>
    </head>
    <body>
        <div class="chat-container">
            <div class="chat-header">
                <h2>🛡️ HISS Security Assistant</h2>
                <p>Hello! I'm your AI security assistant. How can I help you today?</p>
            </div>
            <div class="chat-messages" id="messages"></div>
            <div class="suggestions" id="suggestions"></div>
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="Type your message here..." onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()">Send</button>
            </div>
            <div class="status" id="status">Connected to HISS Security System</div>
        </div>

        <script>
            const ws = new WebSocket("ws://localhost:8000/ws/chat");
            const messages = document.getElementById('messages');
            const messageInput = document.getElementById('messageInput');
            const suggestions = document.getElementById('suggestions');
            const status = document.getElementById('status');

            ws.onopen = function(event) {
                status.textContent = "Connected to HISS Security System";
                addMessage("Welcome! I'm your AI security assistant. How can I help you today?", "system");
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addMessage(data.response, "system");
                updateSuggestions(data.suggestions || []);
            };

            ws.onclose = function(event) {
                status.textContent = "Disconnected from security system";
            };

            function sendMessage() {
                const message = messageInput.value.trim();
                if (message) {
                    addMessage(message, "user");
                    ws.send(JSON.stringify({message: message, visitor_id: "web_user"}));
                    messageInput.value = "";
                }
            }

            function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }

            function addMessage(text, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                messageDiv.textContent = text;
                messages.appendChild(messageDiv);
                messages.scrollTop = messages.scrollHeight;
            }

            function updateSuggestions(suggestionList) {
                suggestions.innerHTML = '';
                suggestionList.forEach(suggestion => {
                    const btn = document.createElement('span');
                    btn.className = 'suggestion-btn';
                    btn.textContent = suggestion;
                    btn.onclick = () => {
                        messageInput.value = suggestion;
                        sendMessage();
                    };
                    suggestions.appendChild(btn);
                });
            }
        </script>
    </body>
    </html>
    """


# Global chat interface instance
_chat_interface = None


def get_chat_interface() -> HissChatInterface:
    """Get the global chat interface instance"""
    global _chat_interface
    if _chat_interface is None:
        _chat_interface = HissChatInterface()
    return _chat_interface
