"""
HISS Demo Script
Comprehensive demonstration of the HISS security system capabilities
"""

import asyncio
import time
from datetime import datetime
from python_client import HissClient
from src.utils.fake_data import get_fake_data_generator


class HissDemo:
    """Demo class for HISS system"""
    
    def __init__(self):
        self.client = HissClient()
        self.fake_data = get_fake_data_generator()
    
    def print_header(self, title: str):
        """Print a formatted header"""
        print(f"\n{'='*60}")
        print(f"🛡️  {title}")
        print(f"{'='*60}")
    
    def print_step(self, step: str, description: str):
        """Print a formatted step"""
        print(f"\n📋 Step {step}: {description}")
        print("-" * 40)
    
    def demo_system_overview(self):
        """Demonstrate system overview and health"""
        self.print_header("HISS SYSTEM OVERVIEW")
        
        try:
            # System status
            status = self.client.get_system_status()
            print(f"✅ System Status: {status['message']}")
            print(f"   Version: {status['data']['version']}")
            print(f"   Timestamp: {status['data']['timestamp']}")
            
            # Health check
            health = self.client.get_health()
            print(f"\n🏥 System Health:")
            print(f"   Status: {health['status']}")
            print(f"   Security Level: {health['security_level']}")
            print(f"   Active Visitors: {health['active_visitors']}")
            print(f"   Pending Decisions: {health['pending_decisions']}")
            
            # Knowledge base info
            kb_info = self.client.get_knowledge_info()
            if kb_info["success"]:
                data = kb_info["data"]
                print(f"\n📚 Knowledge Base:")
                print(f"   Collection: {data.get('name', 'N/A')}")
                print(f"   Documents: {data.get('document_count', 0)}")
                print(f"   Embedding Model: {data.get('embedding_model', 'N/A')}")
            
        except Exception as e:
            print(f"❌ Error in system overview: {e}")
    
    def demo_visitor_registration_and_screening(self):
        """Demonstrate visitor registration and screening process"""
        self.print_header("VISITOR REGISTRATION & SCREENING")
        
        # Generate sample visitors
        visitors = self.fake_data.generate_visitors(3)
        
        for i, visitor in enumerate(visitors, 1):
            self.print_step(f"{i}", f"Processing visitor: {visitor.name}")
            
            try:
                # Register visitor
                visitor_data = {
                    "name": visitor.name,
                    "purpose": visitor.purpose,
                    "company": visitor.company,
                    "contact_email": visitor.contact_email,
                    "contact_phone": visitor.contact_phone,
                    "appointment_with": visitor.appointment_with,
                    "expected_duration": visitor.expected_duration
                }
                
                print(f"👤 Registering: {visitor.name}")
                print(f"   Purpose: {visitor.purpose}")
                print(f"   Company: {visitor.company or 'Individual'}")
                
                registration = self.client.register_visitor(visitor_data)
                visitor_id = registration["data"]["visitor_id"]
                print(f"✅ Registered with ID: {visitor_id[:8]}...")
                
                # Screen visitor
                print(f"\n🔍 Screening visitor...")
                screening = self.client.screen_visitor(visitor_id)
                
                decision_data = screening["data"]
                decision = decision_data["decision"]
                confidence = decision_data["confidence"]
                reason = decision_data["reason"]
                
                status_emoji = "✅" if decision == "ALLOW" else "❌"
                print(f"{status_emoji} Decision: {decision}")
                print(f"   Confidence: {confidence:.2f}")
                print(f"   Reason: {reason}")
                
                if decision_data.get("messages"):
                    print(f"   System Messages: {len(decision_data['messages'])} generated")
                
                time.sleep(2)  # Pause between visitors
                
            except Exception as e:
                print(f"❌ Error processing {visitor.name}: {e}")
    
    def demo_interactive_conversations(self):
        """Demonstrate interactive conversations"""
        self.print_header("INTERACTIVE CONVERSATIONS")
        
        # Create a test visitor for conversation
        visitor_data = {
            "name": "Demo User",
            "purpose": "System demonstration",
            "company": "Demo Corp"
        }
        
        try:
            registration = self.client.register_visitor(visitor_data)
            visitor_id = registration["data"]["visitor_id"]
            
            # Conversation scenarios
            scenarios = [
                {
                    "title": "Greeting and Basic Help",
                    "messages": [
                        "Hello, I'm here for my appointment",
                        "Can you help me with directions to the conference room?"
                    ]
                },
                {
                    "title": "Information Requests",
                    "messages": [
                        "What are the building's WiFi details?",
                        "Where can I find the restrooms?",
                        "What time does the building close?"
                    ]
                },
                {
                    "title": "Problem Resolution",
                    "messages": [
                        "I can't find the person I'm supposed to meet",
                        "My visitor badge isn't working",
                        "I need to extend my visit duration"
                    ]
                }
            ]
            
            for scenario in scenarios:
                self.print_step("", scenario["title"])
                
                for message in scenario["messages"]:
                    print(f"\n👤 Visitor: {message}")
                    
                    try:
                        response = self.client.chat(message, visitor_id)
                        print(f"🤖 HISS: {response['response']}")
                        
                        if response.get('suggestions'):
                            suggestions = response['suggestions'][:2]  # Show first 2
                            print(f"💡 Suggestions: {', '.join(suggestions)}")
                        
                        if response.get('requires_action'):
                            print("⚠️  This response requires follow-up action")
                        
                    except Exception as e:
                        print(f"❌ Chat error: {e}")
                    
                    time.sleep(1.5)
            
            # Show conversation history
            print(f"\n📜 Conversation Summary:")
            conversation = self.client.get_conversation(visitor_id)
            print(f"   Total messages: {len(conversation)}")
            print(f"   Conversation duration: {len(conversation) * 1.5:.1f} seconds (simulated)")
            
        except Exception as e:
            print(f"❌ Error in conversation demo: {e}")
    
    def demo_security_scenarios(self):
        """Demonstrate various security scenarios"""
        self.print_header("SECURITY SCENARIOS")
        
        scenarios = [
            {
                "name": "High-Risk Visitor",
                "data": {
                    "name": "Suspicious Person",
                    "purpose": "Unspecified business",
                    "company": None,
                    "contact_email": "<EMAIL>"
                },
                "expected": "DENY"
            },
            {
                "name": "VIP Visitor",
                "data": {
                    "name": "Important Client",
                    "purpose": "Executive meeting with CEO",
                    "company": "Major Corporation",
                    "appointment_with": "CEO Office",
                    "contact_email": "<EMAIL>"
                },
                "expected": "ALLOW"
            },
            {
                "name": "After-Hours Visit",
                "data": {
                    "name": "Maintenance Worker",
                    "purpose": "Emergency system repair",
                    "company": "TechFix Services",
                    "special_requirements": "After hours access required"
                },
                "expected": "CONDITIONAL"
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            self.print_step(f"{i}", f"Scenario: {scenario['name']}")
            
            try:
                print(f"📝 Visitor Profile:")
                for key, value in scenario['data'].items():
                    if value:
                        print(f"   {key.replace('_', ' ').title()}: {value}")
                
                registration = self.client.register_visitor(scenario['data'])
                visitor_id = registration["data"]["visitor_id"]
                
                screening = self.client.screen_visitor(visitor_id)
                decision_data = screening["data"]
                
                decision = decision_data["decision"]
                confidence = decision_data["confidence"]
                reason = decision_data["reason"]
                
                print(f"\n🔍 Security Analysis:")
                print(f"   Decision: {decision}")
                print(f"   Confidence: {confidence:.2f}")
                print(f"   Reasoning: {reason}")
                
                # Color-coded result
                if decision == "ALLOW":
                    print("✅ ACCESS GRANTED")
                elif decision == "DENY":
                    print("❌ ACCESS DENIED")
                else:
                    print("⚠️  CONDITIONAL ACCESS")
                
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ Error in scenario {scenario['name']}: {e}")
    
    def demo_system_monitoring(self):
        """Demonstrate system monitoring capabilities"""
        self.print_header("SYSTEM MONITORING")
        
        try:
            # Get all visitors
            visitors = self.client.get_visitors()
            print(f"📊 Visitor Statistics:")
            print(f"   Total Registered: {len(visitors)}")
            
            # Count by status
            status_counts = {}
            for visitor in visitors:
                status = visitor.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            for status, count in status_counts.items():
                print(f"   {status.title()}: {count}")
            
            # Recent activity
            print(f"\n📈 Recent Activity:")
            recent_visitors = sorted(visitors, key=lambda x: x.get('created_at', ''), reverse=True)[:3]
            
            for visitor in recent_visitors:
                name = visitor.get('name', 'Unknown')
                status = visitor.get('status', 'unknown')
                purpose = visitor.get('purpose', 'Not specified')
                print(f"   • {name} ({status}) - {purpose}")
            
            # System health over time (simulated)
            print(f"\n💓 System Health Metrics:")
            health = self.client.get_health()
            print(f"   Uptime: Excellent")
            print(f"   Response Time: <100ms")
            print(f"   Security Level: {health['security_level']}")
            print(f"   Active Connections: {len(visitors)}")
            
        except Exception as e:
            print(f"❌ Error in monitoring demo: {e}")
    
    def run_full_demo(self):
        """Run the complete demonstration"""
        print("🚀 Starting HISS Complete Demonstration")
        print(f"⏰ Demo started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            self.demo_system_overview()
            time.sleep(3)
            
            self.demo_visitor_registration_and_screening()
            time.sleep(3)
            
            self.demo_interactive_conversations()
            time.sleep(3)
            
            self.demo_security_scenarios()
            time.sleep(3)
            
            self.demo_system_monitoring()
            
            self.print_header("DEMONSTRATION COMPLETE")
            print("✅ All demo scenarios completed successfully!")
            print("🎯 HISS has demonstrated:")
            print("   • Intelligent visitor screening")
            print("   • Natural language conversations")
            print("   • Security decision making")
            print("   • Real-time monitoring")
            print("   • Comprehensive logging")
            
        except Exception as e:
            print(f"❌ Demo failed: {e}")
        
        print(f"\n⏰ Demo completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def main():
    """Main demo function"""
    demo = HissDemo()
    demo.run_full_demo()


if __name__ == "__main__":
    main()
