"""
Python client example for HISS API
Demonstrates how to interact with the HISS security system
"""

import requests
import json
import time
from typing import Dict, Any, Optional


class HissClient:
    """Python client for HISS API"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status"""
        response = self.session.get(f"{self.base_url}/")
        response.raise_for_status()
        return response.json()
    
    def get_health(self) -> Dict[str, Any]:
        """Get system health"""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def register_visitor(self, visitor_data: Dict[str, Any]) -> Dict[str, Any]:
        """Register a new visitor"""
        response = self.session.post(
            f"{self.base_url}/visitors/register",
            json=visitor_data
        )
        response.raise_for_status()
        return response.json()
    
    def screen_visitor(self, visitor_id: str) -> Dict[str, Any]:
        """Screen a visitor for access decision"""
        response = self.session.post(
            f"{self.base_url}/visitors/{visitor_id}/screen"
        )
        response.raise_for_status()
        return response.json()
    
    def get_visitors(self, status: Optional[str] = None) -> list:
        """Get list of visitors"""
        params = {"status": status} if status else {}
        response = self.session.get(
            f"{self.base_url}/visitors",
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def get_visitor(self, visitor_id: str) -> Dict[str, Any]:
        """Get specific visitor information"""
        response = self.session.get(f"{self.base_url}/visitors/{visitor_id}")
        response.raise_for_status()
        return response.json()
    
    def chat(self, message: str, visitor_id: Optional[str] = None, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Send a chat message to the system"""
        chat_data = {
            "message": message,
            "visitor_id": visitor_id,
            "context": context or {}
        }
        response = self.session.post(
            f"{self.base_url}/chat",
            json=chat_data
        )
        response.raise_for_status()
        return response.json()
    
    def get_conversation(self, visitor_id: str) -> list:
        """Get conversation history for a visitor"""
        response = self.session.get(f"{self.base_url}/conversations/{visitor_id}")
        response.raise_for_status()
        return response.json()
    
    def get_knowledge_info(self) -> Dict[str, Any]:
        """Get knowledge base information"""
        response = self.session.get(f"{self.base_url}/knowledge/info")
        response.raise_for_status()
        return response.json()


def main():
    """Example usage of the HISS client"""
    
    # Initialize client
    client = HissClient()
    
    print("🛡️ HISS Python Client Example")
    print("=" * 40)
    
    try:
        # Check system status
        print("\n1. Checking system status...")
        status = client.get_system_status()
        print(f"✅ System: {status['message']}")
        
        health = client.get_health()
        print(f"✅ Health: {health['status']} (Security Level: {health['security_level']})")
        
        # Register a visitor
        print("\n2. Registering a visitor...")
        visitor_data = {
            "name": "Alice Johnson",
            "purpose": "Business meeting with development team",
            "company": "TechFlow Solutions",
            "contact_email": "<EMAIL>",
            "contact_phone": "******-0199",
            "appointment_with": "Bob Smith",
            "expected_duration": "2 hours"
        }
        
        registration_result = client.register_visitor(visitor_data)
        visitor_id = registration_result["data"]["visitor_id"]
        print(f"✅ Visitor registered: {visitor_id}")
        
        # Screen the visitor
        print("\n3. Screening visitor...")
        screening_result = client.screen_visitor(visitor_id)
        decision = screening_result["data"]["decision"]
        confidence = screening_result["data"]["confidence"]
        reason = screening_result["data"]["reason"]
        
        print(f"✅ Decision: {decision} (Confidence: {confidence:.2f})")
        print(f"   Reason: {reason}")
        
        # Chat with the system
        print("\n4. Testing chat functionality...")
        chat_messages = [
            "Hello, I'm here for my appointment",
            "Can you help me find the conference room?",
            "What are the building's WiFi details?",
            "Thank you for your help!"
        ]
        
        for message in chat_messages:
            print(f"\n👤 Visitor: {message}")
            chat_response = client.chat(message, visitor_id)
            print(f"🤖 HISS: {chat_response['response']}")
            
            if chat_response.get('suggestions'):
                print(f"💡 Suggestions: {', '.join(chat_response['suggestions'])}")
            
            time.sleep(1)  # Brief pause between messages
        
        # Get visitor information
        print("\n5. Getting visitor information...")
        visitor_info = client.get_visitor(visitor_id)
        print(f"✅ Visitor: {visitor_info['name']} - Status: {visitor_info['status']}")
        
        # Get conversation history
        print("\n6. Getting conversation history...")
        conversation = client.get_conversation(visitor_id)
        print(f"✅ Conversation has {len(conversation)} messages")
        
        # List all visitors
        print("\n7. Listing all visitors...")
        all_visitors = client.get_visitors()
        print(f"✅ Total visitors: {len(all_visitors)}")
        
        # Get knowledge base info
        print("\n8. Getting knowledge base information...")
        kb_info = client.get_knowledge_info()
        if kb_info["success"]:
            data = kb_info["data"]
            print(f"✅ Knowledge Base: {data.get('document_count', 0)} documents")
        
        print("\n🎉 All tests completed successfully!")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API Error: {e}")
    except KeyError as e:
        print(f"❌ Response format error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


def interactive_chat():
    """Interactive chat session with HISS"""
    
    client = HissClient()
    visitor_id = "interactive_user"
    
    print("🛡️ HISS Interactive Chat")
    print("Type 'quit' to exit")
    print("=" * 30)
    
    try:
        while True:
            message = input("\n👤 You: ").strip()
            
            if message.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if not message:
                continue
            
            try:
                response = client.chat(message, visitor_id)
                print(f"🤖 HISS: {response['response']}")
                
                if response.get('suggestions'):
                    print(f"💡 Suggestions: {', '.join(response['suggestions'])}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
    
    except KeyboardInterrupt:
        print("\n👋 Chat session ended.")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "chat":
        interactive_chat()
    else:
        main()
