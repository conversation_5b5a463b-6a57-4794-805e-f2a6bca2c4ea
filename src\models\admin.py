"""
Admin and SubAdmin models for HISS
Handles user roles, permissions, and authentication
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, validator
import uuid
import hashlib


class UserRole(str, Enum):
    """User roles in the system"""
    ADMIN = "admin"
    SUBADMIN = "subadmin"
    VISITOR = "visitor"
    BOT = "bot"


class PermissionType(str, Enum):
    """Permission types"""
    VIEW_ALL_CONVERSATIONS = "view_all_conversations"
    MANAGE_USERS = "manage_users"
    APPROVE_VISITORS = "approve_visitors"
    SYSTEM_SETTINGS = "system_settings"
    VIEW_ANALYTICS = "view_analytics"
    EMERGENCY_OVERRIDE = "emergency_override"


class RequestStatus(str, Enum):
    """Status of permission requests"""
    PENDING = "pending"
    APPROVED = "approved"
    DENIED = "denied"
    EXPIRED = "expired"


class User(BaseModel):
    """User model for admin hierarchy"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., regex=r'^[^@]+@[^@]+\.[^@]+$')
    full_name: str = Field(..., min_length=1, max_length=100)
    role: UserRole
    is_active: bool = Field(default=True)
    permissions: List[PermissionType] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.now)
    last_login: Optional[datetime] = None
    password_hash: Optional[str] = None
    phone_number: Optional[str] = Field(None, max_length=20)
    department: Optional[str] = Field(None, max_length=50)
    
    def set_password(self, password: str):
        """Set password hash"""
        self.password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str) -> bool:
        """Verify password"""
        if not self.password_hash:
            return False
        return self.password_hash == hashlib.sha256(password.encode()).hexdigest()
    
    def has_permission(self, permission: PermissionType) -> bool:
        """Check if user has specific permission"""
        return permission in self.permissions
    
    class Config:
        use_enum_values = True


class PermissionRequest(BaseModel):
    """Permission request from bot to subadmin"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    visitor_id: str
    visitor_name: str
    visitor_purpose: str
    visitor_details: Dict[str, Any] = Field(default_factory=dict)
    bot_conversation_summary: str
    bot_recommendation: str
    bot_confidence: float = Field(..., ge=0.0, le=1.0)
    requested_by: str = Field(default="HISS Bot")
    requested_at: datetime = Field(default_factory=datetime.now)
    status: RequestStatus = Field(default=RequestStatus.PENDING)
    assigned_to: Optional[str] = None  # SubAdmin ID
    reviewed_by: Optional[str] = None
    reviewed_at: Optional[datetime] = None
    decision_reason: Optional[str] = None
    expires_at: Optional[datetime] = None
    priority: int = Field(default=1, ge=1, le=5)  # 1=low, 5=urgent
    
    class Config:
        use_enum_values = True


class ConversationSession(BaseModel):
    """Enhanced conversation session tracking"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    visitor_id: Optional[str] = None
    visitor_name: Optional[str] = None
    session_type: str = Field(default="outdoor_interaction")  # outdoor_interaction, indoor_chat, admin_review
    started_at: datetime = Field(default_factory=datetime.now)
    ended_at: Optional[datetime] = None
    location: str = Field(default="outdoor")  # outdoor, lobby, indoor
    total_messages: int = Field(default=0)
    bot_initiated: bool = Field(default=True)
    permission_requested: bool = Field(default=False)
    permission_request_id: Optional[str] = None
    final_decision: Optional[str] = None  # allowed, denied, abandoned
    session_summary: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True


class AdminAction(BaseModel):
    """Admin action logging"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    admin_id: str
    admin_name: str
    action_type: str  # view_conversations, approve_request, deny_request, etc.
    target_id: Optional[str] = None  # ID of affected resource
    target_type: Optional[str] = None  # conversation, request, user, etc.
    description: str
    timestamp: datetime = Field(default_factory=datetime.now)
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    additional_data: Dict[str, Any] = Field(default_factory=dict)


class NotificationPreferences(BaseModel):
    """User notification preferences"""
    user_id: str
    email_notifications: bool = Field(default=True)
    sms_notifications: bool = Field(default=False)
    push_notifications: bool = Field(default=True)
    notification_types: List[str] = Field(default_factory=lambda: [
        "permission_requests", "emergency_alerts", "system_updates"
    ])
    quiet_hours_start: Optional[str] = Field(None, regex=r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$')
    quiet_hours_end: Optional[str] = Field(None, regex=r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$')


class SystemAlert(BaseModel):
    """System alerts for admins"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    alert_type: str  # permission_request, security_breach, system_error, etc.
    severity: str = Field(default="medium")  # low, medium, high, critical
    title: str = Field(..., min_length=1, max_length=100)
    message: str = Field(..., min_length=1, max_length=500)
    source: str = Field(default="HISS System")
    created_at: datetime = Field(default_factory=datetime.now)
    acknowledged: bool = Field(default=False)
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    resolved: bool = Field(default=False)
    resolved_by: Optional[str] = None
    resolved_at: Optional[datetime] = None
    related_id: Optional[str] = None  # Related request, conversation, etc.
    metadata: Dict[str, Any] = Field(default_factory=dict)


# Default permissions for each role
ROLE_PERMISSIONS = {
    UserRole.ADMIN: [
        PermissionType.VIEW_ALL_CONVERSATIONS,
        PermissionType.MANAGE_USERS,
        PermissionType.APPROVE_VISITORS,
        PermissionType.SYSTEM_SETTINGS,
        PermissionType.VIEW_ANALYTICS,
        PermissionType.EMERGENCY_OVERRIDE
    ],
    UserRole.SUBADMIN: [
        PermissionType.APPROVE_VISITORS,
        PermissionType.VIEW_ANALYTICS
    ],
    UserRole.VISITOR: [],
    UserRole.BOT: []
}


def create_default_admin() -> User:
    """Create default admin user"""
    admin = User(
        username="admin",
        email="<EMAIL>",
        full_name="System Administrator",
        role=UserRole.ADMIN,
        permissions=ROLE_PERMISSIONS[UserRole.ADMIN],
        department="Security"
    )
    admin.set_password("admin123")  # Default password - should be changed
    return admin


def create_default_subadmin() -> User:
    """Create default subadmin user"""
    subadmin = User(
        username="subadmin",
        email="<EMAIL>",
        full_name="Security Supervisor",
        role=UserRole.SUBADMIN,
        permissions=ROLE_PERMISSIONS[UserRole.SUBADMIN],
        department="Security"
    )
    subadmin.set_password("subadmin123")  # Default password - should be changed
    return subadmin
