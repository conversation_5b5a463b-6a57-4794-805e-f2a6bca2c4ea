# HISS Configuration
# Copy this file to .env and fill in your actual values

# Google Gemini API Configuration
GOOGLE_API_KEY=your_gemini_api_key_here

# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# ChromaDB Configuration
CHROMA_DB_PATH=./data/chroma_db
CHROMA_COLLECTION_NAME=hiss_security_knowledge

# FastAPI Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# Security System Configuration
SYSTEM_NAME=HISS Security Guard
MAX_VISITORS_PER_DAY=100
SECURITY_LEVEL=high

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/hiss.log
