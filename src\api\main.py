"""
FastAPI main application for HISS
Provides REST API endpoints for the security system
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>po<PERSON>, HTMLResponse
from typing import List, Dict, Any, Optional
import asyncio
import json
from datetime import datetime

from ..models.security import (
    VisitorInfo, VisitorRegistrationRequest, SecurityDecision,
    ChatRequest, ChatResponse, SystemResponse, SystemStatus,
    ConversationMessage, AccessLog, SecurityAlert
)
from ..agents.security_agent import get_security_agent
from ..database.vector_store import get_vector_store
from ..database.document_loader import get_document_loader
from ..utils.config import get_settings
from ..utils.logger import get_logger, log_security_event, log_interaction
from .chat_interface import manager, get_chat_interface, get_chat_html
from .admin_dashboard import router as admin_router
from .subadmin_panel import router as subadmin_router
from .outdoor_interface import router as outdoor_router
from ..agents.outdoor_bot import get_outdoor_bot

settings = get_settings()
logger = get_logger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="HISS - Human Interactive Security System",
    description="An Agentic RAG system for intelligent security management",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(admin_router)
app.include_router(subadmin_router)
app.include_router(outdoor_router)

# Global state (in production, use proper database)
visitors_db: Dict[str, VisitorInfo] = {}
decisions_db: Dict[str, SecurityDecision] = {}
conversations_db: Dict[str, List[ConversationMessage]] = {}


@app.on_event("startup")
async def startup_event():
    """Initialize the system on startup"""
    logger.info("Starting HISS system...")
    
    # Initialize knowledge base
    try:
        document_loader = get_document_loader()
        success = document_loader.initialize_knowledge_base()
        if success:
            logger.info("Knowledge base initialized successfully")
        else:
            logger.warning("Knowledge base initialization failed")
    except Exception as e:
        logger.error(f"Failed to initialize knowledge base: {e}")


@app.get("/", response_model=SystemResponse)
async def root():
    """Root endpoint with system information"""
    return SystemResponse(
        success=True,
        message=f"Welcome to {settings.system_name}",
        data={
            "version": "1.0.0",
            "status": "online",
            "timestamp": datetime.now().isoformat()
        }
    )


@app.get("/health", response_model=SystemStatus)
async def health_check():
    """Health check endpoint"""
    try:
        vector_store = get_vector_store()
        collection_info = vector_store.get_collection_info()
        
        return SystemStatus(
            status="online",
            security_level=settings.security_level,
            active_visitors=len([v for v in visitors_db.values() if v.status == "inside"]),
            pending_decisions=len([d for d in decisions_db.values() if d.decision == "PENDING"]),
            active_alerts=0,  # Would be calculated from alerts database
            system_version="1.0.0",
            uptime="N/A"  # Would be calculated from startup time
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="System health check failed")


@app.post("/visitors/register", response_model=SystemResponse)
async def register_visitor(visitor_request: VisitorRegistrationRequest):
    """Register a new visitor"""
    try:
        # Create visitor info
        visitor = VisitorInfo(**visitor_request.dict())
        visitors_db[visitor.id] = visitor
        
        log_security_event(
            event_type="visitor_registration",
            visitor_id=visitor.id,
            additional_data={"name": visitor.name, "purpose": visitor.purpose}
        )
        
        return SystemResponse(
            success=True,
            message="Visitor registered successfully",
            data={"visitor_id": visitor.id, "status": visitor.status}
        )
        
    except Exception as e:
        logger.error(f"Visitor registration failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/visitors/{visitor_id}/screen", response_model=SystemResponse)
async def screen_visitor(visitor_id: str):
    """Screen a visitor for access decision"""
    try:
        if visitor_id not in visitors_db:
            raise HTTPException(status_code=404, detail="Visitor not found")
        
        visitor = visitors_db[visitor_id]
        security_agent = get_security_agent()
        
        # Process visitor through security workflow
        result = await security_agent.process_visitor(visitor.dict())
        
        if result["success"] and result["security_decision"]:
            # Store decision
            decision_data = result["security_decision"]
            decision = SecurityDecision(**decision_data)
            decisions_db[decision.id] = decision
            
            # Update visitor status
            if decision.decision == "ALLOW":
                visitor.status = "checked_in"
            else:
                visitor.status = "denied"
            
            return SystemResponse(
                success=True,
                message="Visitor screening completed",
                data={
                    "decision": decision.decision,
                    "confidence": decision.confidence,
                    "reason": decision.reason,
                    "decision_id": decision.id,
                    "messages": result.get("messages", [])
                }
            )
        else:
            return SystemResponse(
                success=False,
                message="Visitor screening failed",
                data={"error": result.get("error"), "requires_human_intervention": result.get("requires_human_intervention")}
            )
            
    except Exception as e:
        logger.error(f"Visitor screening failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/chat", response_model=ChatResponse)
async def chat_with_system(chat_request: ChatRequest):
    """Chat with the security system"""
    try:
        security_agent = get_security_agent()
        
        # Get visitor context if provided
        visitor_context = None
        if chat_request.visitor_id and chat_request.visitor_id in visitors_db:
            visitor_context = visitors_db[chat_request.visitor_id].dict()
        
        # Get conversation history
        conversation_history = conversations_db.get(chat_request.visitor_id or "anonymous", [])
        
        # Generate response using LLM
        from ..utils.llm import get_llm
        from ..prompts.templates import get_prompt_templates
        
        llm = get_llm()
        prompts = get_prompt_templates()
        
        response_text = llm.generate_conversation_response(
            message=chat_request.message,
            conversation_history=[msg.dict() for msg in conversation_history[-5:]],
            visitor_context=visitor_context,
            system_prompt=prompts.security_guard_system_prompt
        )
        
        # Create conversation messages
        user_message = ConversationMessage(
            visitor_id=chat_request.visitor_id,
            role="visitor",
            content=chat_request.message,
            message_type="question"
        )
        
        system_message = ConversationMessage(
            visitor_id=chat_request.visitor_id,
            role="security_guard",
            content=response_text,
            message_type="response"
        )
        
        # Store conversation
        conversation_key = chat_request.visitor_id or "anonymous"
        if conversation_key not in conversations_db:
            conversations_db[conversation_key] = []
        
        conversations_db[conversation_key].extend([user_message, system_message])
        
        # Log interaction
        log_interaction(
            interaction_type="chat",
            visitor_id=chat_request.visitor_id,
            message=chat_request.message,
            response=response_text
        )
        
        return ChatResponse(
            message_id=system_message.id,
            response=response_text,
            suggestions=["Can you help me with directions?", "What are the visiting hours?", "Who should I contact?"]
        )
        
    except Exception as e:
        logger.error(f"Chat interaction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/visitors", response_model=List[VisitorInfo])
async def list_visitors(status: Optional[str] = None):
    """List all visitors, optionally filtered by status"""
    try:
        visitors = list(visitors_db.values())
        
        if status:
            visitors = [v for v in visitors if v.status == status]
        
        return visitors
        
    except Exception as e:
        logger.error(f"Failed to list visitors: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/visitors/{visitor_id}", response_model=VisitorInfo)
async def get_visitor(visitor_id: str):
    """Get visitor information"""
    if visitor_id not in visitors_db:
        raise HTTPException(status_code=404, detail="Visitor not found")
    
    return visitors_db[visitor_id]


@app.get("/decisions", response_model=List[SecurityDecision])
async def list_decisions():
    """List all security decisions"""
    return list(decisions_db.values())


@app.get("/decisions/{decision_id}", response_model=SecurityDecision)
async def get_decision(decision_id: str):
    """Get specific security decision"""
    if decision_id not in decisions_db:
        raise HTTPException(status_code=404, detail="Decision not found")
    
    return decisions_db[decision_id]


@app.get("/conversations/{visitor_id}", response_model=List[ConversationMessage])
async def get_conversation(visitor_id: str):
    """Get conversation history for a visitor"""
    return conversations_db.get(visitor_id, [])


@app.post("/system/reset", response_model=SystemResponse)
async def reset_system():
    """Reset the system (development only)"""
    try:
        global visitors_db, decisions_db, conversations_db
        visitors_db.clear()
        decisions_db.clear()
        conversations_db.clear()
        
        logger.warning("System has been reset")
        
        return SystemResponse(
            success=True,
            message="System reset successfully"
        )
        
    except Exception as e:
        logger.error(f"System reset failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/knowledge/info", response_model=SystemResponse)
async def get_knowledge_info():
    """Get information about the knowledge base"""
    try:
        vector_store = get_vector_store()
        info = vector_store.get_collection_info()
        
        return SystemResponse(
            success=True,
            message="Knowledge base information retrieved",
            data=info
        )
        
    except Exception as e:
        logger.error(f"Failed to get knowledge info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/chat", response_class=HTMLResponse)
async def get_chat_page():
    """Get the chat interface HTML page"""
    return get_chat_html()


@app.post("/outdoor/start")
async def start_outdoor_interaction(visitor_description: str = None):
    """Start a new outdoor interaction"""
    try:
        outdoor_bot = get_outdoor_bot()
        interaction_id = outdoor_bot.start_outdoor_interaction(visitor_description)

        return SystemResponse(
            success=True,
            message="Outdoor interaction started",
            data={"interaction_id": interaction_id}
        )

    except Exception as e:
        logger.error(f"Failed to start outdoor interaction: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/outdoor/{interaction_id}/message")
async def send_outdoor_message(
    interaction_id: str,
    message: str,
    visitor_name: str = None
):
    """Send message in outdoor interaction"""
    try:
        outdoor_bot = get_outdoor_bot()
        response, needs_permission = outdoor_bot.process_outdoor_message(
            interaction_id, message, visitor_name
        )

        return SystemResponse(
            success=True,
            message="Message processed",
            data={
                "response": response,
                "needs_permission": needs_permission,
                "interaction_id": interaction_id
            }
        )

    except Exception as e:
        logger.error(f"Failed to process outdoor message: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/outdoor/{interaction_id}/status")
async def get_outdoor_status(interaction_id: str):
    """Get outdoor interaction status"""
    try:
        outdoor_bot = get_outdoor_bot()
        status = outdoor_bot.get_interaction_status(interaction_id)

        return SystemResponse(
            success=True,
            message="Status retrieved",
            data=status
        )

    except Exception as e:
        logger.error(f"Failed to get outdoor status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/ws/chat")
async def websocket_chat_endpoint(websocket: WebSocket, visitor_id: str = "anonymous"):
    """WebSocket endpoint for real-time chat"""
    await manager.connect(websocket, visitor_id)
    chat_interface = get_chat_interface()

    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # Process the message
            response = await chat_interface.process_message(
                visitor_id=message_data.get("visitor_id", visitor_id),
                message=message_data.get("message", ""),
                visitor_context=message_data.get("context")
            )

            # Send response back to client
            await manager.send_personal_message(
                json.dumps(response.dict()),
                websocket
            )

    except WebSocketDisconnect:
        manager.disconnect(websocket, visitor_id)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket, visitor_id)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=settings.api_host, port=settings.api_port)
