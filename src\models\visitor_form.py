"""
Enhanced visitor information form models for home entry requests
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, validator
import uuid


class VisitPurpose(str, Enum):
    """Visit purpose categories"""
    FAMILY_VISIT = "family_visit"
    FRIEND_VISIT = "friend_visit"
    BUSINESS_MEETING = "business_meeting"
    DELIVERY = "delivery"
    SERVICE_CALL = "service_call"
    MAINTENANCE = "maintenance"
    EMERGENCY = "emergency"
    SOCIAL_VISIT = "social_visit"
    OTHER = "other"


class RelationshipType(str, Enum):
    """Relationship to homeowner"""
    FAMILY_MEMBER = "family_member"
    CLOSE_FRIEND = "close_friend"
    COLLEAGUE = "colleague"
    NEIGHBOR = "neighbor"
    SERVICE_PROVIDER = "service_provider"
    DELIVERY_PERSON = "delivery_person"
    ACQUAINTANCE = "acquaintance"
    FIRST_TIME_VISITOR = "first_time_visitor"
    OTHER = "other"


class UrgencyLevel(str, Enum):
    """Urgency level of the visit"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"
    EMERGENCY = "emergency"


class IdentificationType(str, Enum):
    """Types of identification"""
    DRIVERS_LICENSE = "drivers_license"
    PASSPORT = "passport"
    NATIONAL_ID = "national_id"
    EMPLOYEE_ID = "employee_id"
    STUDENT_ID = "student_id"
    OTHER_GOVERNMENT_ID = "other_government_id"
    NO_ID = "no_id"


class ComprehensiveVisitorForm(BaseModel):
    """Comprehensive visitor information form for home entry"""
    
    # Basic Information
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    full_name: str = Field(..., min_length=2, max_length=100)
    preferred_name: Optional[str] = Field(None, max_length=50)
    date_of_birth: Optional[date] = None
    phone_number: str = Field(..., min_length=10, max_length=20)
    email_address: Optional[str] = Field(None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    
    # Identification
    id_type: IdentificationType
    id_number: Optional[str] = Field(None, max_length=50)
    id_issuing_authority: Optional[str] = Field(None, max_length=100)
    id_expiry_date: Optional[date] = None
    
    # Address Information
    current_address: str = Field(..., min_length=10, max_length=200)
    city: str = Field(..., min_length=2, max_length=50)
    state_province: str = Field(..., min_length=2, max_length=50)
    postal_code: str = Field(..., min_length=3, max_length=20)
    country: str = Field(default="United States", max_length=50)
    
    # Visit Details
    visit_purpose: VisitPurpose
    visit_purpose_details: str = Field(..., min_length=10, max_length=500)
    relationship_to_homeowner: RelationshipType
    relationship_details: Optional[str] = Field(None, max_length=200)
    person_to_visit: str = Field(..., min_length=2, max_length=100)
    expected_duration: str = Field(..., max_length=50)
    urgency_level: UrgencyLevel = Field(default=UrgencyLevel.MEDIUM)
    
    # Appointment Information
    has_appointment: bool = Field(default=False)
    appointment_time: Optional[datetime] = None
    appointment_confirmed_by: Optional[str] = Field(None, max_length=100)
    appointment_reference: Optional[str] = Field(None, max_length=50)
    
    # Vehicle Information
    has_vehicle: bool = Field(default=False)
    vehicle_make: Optional[str] = Field(None, max_length=50)
    vehicle_model: Optional[str] = Field(None, max_length=50)
    vehicle_color: Optional[str] = Field(None, max_length=30)
    license_plate: Optional[str] = Field(None, max_length=20)
    parking_needed: bool = Field(default=False)
    
    # Emergency Contact
    emergency_contact_name: str = Field(..., min_length=2, max_length=100)
    emergency_contact_phone: str = Field(..., min_length=10, max_length=20)
    emergency_contact_relationship: str = Field(..., max_length=50)
    
    # Additional Information
    special_requirements: Optional[str] = Field(None, max_length=300)
    medical_conditions: Optional[str] = Field(None, max_length=200)
    allergies: Optional[str] = Field(None, max_length=200)
    previous_visits: bool = Field(default=False)
    last_visit_date: Optional[date] = None
    
    # Security Information
    carrying_items: Optional[str] = Field(None, max_length=200)
    electronic_devices: Optional[str] = Field(None, max_length=200)
    weapons_declaration: bool = Field(default=False)
    
    # Verification
    photo_id_verified: bool = Field(default=False)
    background_check_consent: bool = Field(default=False)
    terms_accepted: bool = Field(default=False)
    
    # System Fields
    form_completed_at: datetime = Field(default_factory=datetime.now)
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    location_coordinates: Optional[str] = None
    
    @validator('phone_number', 'emergency_contact_phone')
    def validate_phone(cls, v):
        # Remove non-digits and validate length
        digits = ''.join(filter(str.isdigit, v))
        if len(digits) < 10:
            raise ValueError('Phone number must have at least 10 digits')
        return v
    
    @validator('date_of_birth')
    def validate_age(cls, v):
        if v and v > date.today():
            raise ValueError('Date of birth cannot be in the future')
        if v and (date.today() - v).days < 365 * 13:  # Must be at least 13 years old
            raise ValueError('Visitor must be at least 13 years old')
        return v
    
    class Config:
        use_enum_values = True


class VisitorFormResponse(BaseModel):
    """Response after form submission"""
    form_id: str
    submission_status: str
    validation_errors: List[str] = Field(default_factory=list)
    next_steps: List[str] = Field(default_factory=list)
    estimated_processing_time: str
    reference_number: str


class EnhancedPermissionRequest(BaseModel):
    """Enhanced permission request with complete visitor information"""
    
    # Basic Request Info
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    visitor_form: ComprehensiveVisitorForm
    
    # Bot Analysis
    conversation_summary: str = Field(..., min_length=50)
    bot_risk_assessment: str
    bot_recommendation: str
    bot_confidence: float = Field(..., ge=0.0, le=1.0)
    security_flags: List[str] = Field(default_factory=list)
    
    # AI Analysis Results
    identity_verification_score: float = Field(..., ge=0.0, le=1.0)
    purpose_legitimacy_score: float = Field(..., ge=0.0, le=1.0)
    relationship_credibility_score: float = Field(..., ge=0.0, le=1.0)
    overall_trust_score: float = Field(..., ge=0.0, le=1.0)
    
    # Request Metadata
    requested_at: datetime = Field(default_factory=datetime.now)
    priority_level: int = Field(default=3, ge=1, le=5)
    auto_approval_eligible: bool = Field(default=False)
    requires_additional_verification: bool = Field(default=False)
    
    # Processing Info
    status: str = Field(default="pending")
    assigned_to: Optional[str] = None
    processing_notes: List[str] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True


class VisitorVerificationResult(BaseModel):
    """Result of visitor verification process"""
    
    verification_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    visitor_form_id: str
    
    # Verification Checks
    identity_verified: bool = Field(default=False)
    address_verified: bool = Field(default=False)
    phone_verified: bool = Field(default=False)
    emergency_contact_verified: bool = Field(default=False)
    background_check_passed: bool = Field(default=False)
    
    # Risk Assessment
    risk_level: str = Field(default="medium")  # low, medium, high, critical
    risk_factors: List[str] = Field(default_factory=list)
    security_recommendations: List[str] = Field(default_factory=list)
    
    # Verification Details
    verification_method: str
    verification_timestamp: datetime = Field(default_factory=datetime.now)
    verified_by: str
    verification_notes: Optional[str] = None
    
    # Additional Checks
    watchlist_check: bool = Field(default=False)
    previous_incidents: List[str] = Field(default_factory=list)
    reference_checks: List[Dict[str, Any]] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True


class HomeEntryDecision(BaseModel):
    """Final decision for home entry request"""
    
    decision_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    permission_request_id: str
    
    # Decision Details
    decision: str  # APPROVED, DENIED, CONDITIONAL, PENDING_VERIFICATION
    decision_reason: str = Field(..., min_length=10)
    conditions: List[str] = Field(default_factory=list)
    restrictions: List[str] = Field(default_factory=list)
    
    # Approval Details (if approved)
    approved_duration: Optional[str] = None
    escort_required: bool = Field(default=False)
    areas_accessible: List[str] = Field(default_factory=list)
    areas_restricted: List[str] = Field(default_factory=list)
    
    # Decision Maker
    decided_by: str
    decision_timestamp: datetime = Field(default_factory=datetime.now)
    decision_authority: str  # subadmin, admin, system
    
    # Validity
    valid_from: datetime = Field(default_factory=datetime.now)
    valid_until: Optional[datetime] = None
    can_be_extended: bool = Field(default=False)
    
    # Monitoring
    requires_monitoring: bool = Field(default=False)
    check_in_required: bool = Field(default=False)
    check_in_interval: Optional[str] = None
    
    class Config:
        use_enum_values = True
