"""
ChromaDB Vector Store implementation for HISS
Handles document storage, retrieval, and similarity search
"""

import chromadb
from chromadb.config import Settings as ChromaSettings
from typing import List, Dict, Any, Optional
from langchain_community.vectorstores import Chroma
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from ..utils.config import get_settings
from ..utils.logger import get_logger

settings = get_settings()
logger = get_logger(__name__)


class HissVectorStore:
    """Vector store for HISS security knowledge base"""
    
    def __init__(self):
        self.settings = settings
        self.embeddings = GoogleGenerativeAIEmbeddings(
            model=settings.embedding_model,
            google_api_key=settings.google_api_key
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap,
            length_function=len,
        )
        self.vector_store = None
        self._initialize_vector_store()
    
    def _initialize_vector_store(self):
        """Initialize ChromaDB vector store"""
        try:
            # Create ChromaDB client
            chroma_client = chromadb.PersistentClient(
                path=settings.chroma_db_path,
                settings=ChromaSettings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Initialize Langchain Chroma wrapper
            self.vector_store = Chroma(
                client=chroma_client,
                collection_name=settings.chroma_collection_name,
                embedding_function=self.embeddings,
                persist_directory=settings.chroma_db_path
            )
            
            logger.info("Vector store initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize vector store: {e}")
            raise
    
    def add_documents(self, documents: List[Document]) -> List[str]:
        """Add documents to the vector store"""
        try:
            # Split documents into chunks
            split_docs = self.text_splitter.split_documents(documents)
            
            # Add to vector store
            doc_ids = self.vector_store.add_documents(split_docs)
            
            logger.info(f"Added {len(split_docs)} document chunks to vector store")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            raise
    
    def add_text(self, texts: List[str], metadatas: List[Dict] = None) -> List[str]:
        """Add raw texts to the vector store"""
        try:
            doc_ids = self.vector_store.add_texts(
                texts=texts,
                metadatas=metadatas or [{}] * len(texts)
            )
            
            logger.info(f"Added {len(texts)} texts to vector store")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Failed to add texts: {e}")
            raise
    
    def similarity_search(
        self, 
        query: str, 
        k: int = 5,
        filter_dict: Dict[str, Any] = None
    ) -> List[Document]:
        """Perform similarity search"""
        try:
            results = self.vector_store.similarity_search(
                query=query,
                k=k,
                filter=filter_dict
            )
            
            logger.info(f"Similarity search returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Similarity search failed: {e}")
            raise
    
    def similarity_search_with_score(
        self, 
        query: str, 
        k: int = 5,
        filter_dict: Dict[str, Any] = None
    ) -> List[tuple]:
        """Perform similarity search with relevance scores"""
        try:
            results = self.vector_store.similarity_search_with_score(
                query=query,
                k=k,
                filter=filter_dict
            )
            
            logger.info(f"Similarity search with scores returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Similarity search with scores failed: {e}")
            raise
    
    def get_relevant_documents(
        self, 
        query: str, 
        context_type: str = None,
        max_docs: int = 5
    ) -> List[Document]:
        """Get relevant documents for a specific context"""
        try:
            filter_dict = {}
            if context_type:
                filter_dict["context_type"] = context_type
            
            results = self.similarity_search(
                query=query,
                k=max_docs,
                filter_dict=filter_dict
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get relevant documents: {e}")
            return []
    
    def delete_documents(self, doc_ids: List[str]):
        """Delete documents from vector store"""
        try:
            self.vector_store.delete(ids=doc_ids)
            logger.info(f"Deleted {len(doc_ids)} documents from vector store")
            
        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            raise
    
    def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection"""
        try:
            collection = self.vector_store._collection
            count = collection.count()
            
            return {
                "name": settings.chroma_collection_name,
                "document_count": count,
                "embedding_model": settings.embedding_model
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {}
    
    def reset_collection(self):
        """Reset the entire collection (use with caution)"""
        try:
            self.vector_store.delete_collection()
            self._initialize_vector_store()
            logger.warning("Vector store collection has been reset")
            
        except Exception as e:
            logger.error(f"Failed to reset collection: {e}")
            raise


# Global vector store instance
_vector_store_instance = None


def get_vector_store() -> HissVectorStore:
    """Get the global vector store instance"""
    global _vector_store_instance
    if _vector_store_instance is None:
        _vector_store_instance = HissVectorStore()
    return _vector_store_instance
