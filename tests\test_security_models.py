"""
Tests for HISS security models
"""

import pytest
from datetime import datetime, timedelta
from src.models.security import (
    VisitorInfo, SecurityDecision, AccessDecision, SecurityLevel,
    ConversationMessage, VisitorRegistrationRequest, ChatRequest
)


class TestVisitorInfo:
    """Test VisitorInfo model"""
    
    def test_visitor_creation(self):
        """Test creating a visitor with valid data"""
        visitor = VisitorInfo(
            name="<PERSON>",
            purpose="Business meeting",
            company="TechCorp"
        )
        
        assert visitor.name == "<PERSON>"
        assert visitor.purpose == "Business meeting"
        assert visitor.company == "TechCorp"
        assert visitor.status == "registered"
        assert visitor.id is not None
        assert isinstance(visitor.created_at, datetime)
    
    def test_visitor_validation(self):
        """Test visitor data validation"""
        # Test invalid email
        with pytest.raises(ValueError):
            VisitorInfo(
                name="<PERSON>",
                purpose="Meeting",
                contact_email="invalid-email"
            )
    
    def test_visitor_optional_fields(self):
        """Test visitor with optional fields"""
        visitor = VisitorInfo(
            name="<PERSON>",
            purpose="Consultation",
            id_number="12345678",
            contact_phone="******-0123",
            vehicle_info="Blue Toyota - ABC123"
        )
        
        assert visitor.id_number == "12345678"
        assert visitor.contact_phone == "******-0123"
        assert visitor.vehicle_info == "Blue Toyota - ABC123"


class TestSecurityDecision:
    """Test SecurityDecision model"""
    
    def test_decision_creation(self):
        """Test creating a security decision"""
        decision = SecurityDecision(
            visitor_id="visitor-123",
            decision=AccessDecision.ALLOW,
            confidence=0.85,
            reason="Valid business purpose and proper identification"
        )
        
        assert decision.visitor_id == "visitor-123"
        assert decision.decision == AccessDecision.ALLOW
        assert decision.confidence == 0.85
        assert decision.reason == "Valid business purpose and proper identification"
        assert decision.security_level == SecurityLevel.MEDIUM
        assert isinstance(decision.decided_at, datetime)
    
    def test_decision_validation(self):
        """Test decision validation"""
        # Test invalid confidence range
        with pytest.raises(ValueError):
            SecurityDecision(
                visitor_id="visitor-123",
                decision=AccessDecision.DENY,
                confidence=1.5,  # Invalid: > 1.0
                reason="Test reason"
            )
        
        with pytest.raises(ValueError):
            SecurityDecision(
                visitor_id="visitor-123",
                decision=AccessDecision.DENY,
                confidence=-0.1,  # Invalid: < 0.0
                reason="Test reason"
            )


class TestConversationMessage:
    """Test ConversationMessage model"""
    
    def test_message_creation(self):
        """Test creating a conversation message"""
        message = ConversationMessage(
            visitor_id="visitor-123",
            role="visitor",
            content="Hello, I need help with directions"
        )
        
        assert message.visitor_id == "visitor-123"
        assert message.role == "visitor"
        assert message.content == "Hello, I need help with directions"
        assert isinstance(message.timestamp, datetime)
        assert message.id is not None
    
    def test_message_validation(self):
        """Test message validation"""
        # Test empty content
        with pytest.raises(ValueError):
            ConversationMessage(
                role="visitor",
                content=""  # Invalid: empty content
            )
        
        # Test content too long
        with pytest.raises(ValueError):
            ConversationMessage(
                role="visitor",
                content="x" * 2001  # Invalid: > 2000 characters
            )


class TestVisitorRegistrationRequest:
    """Test VisitorRegistrationRequest model"""
    
    def test_registration_request(self):
        """Test creating a visitor registration request"""
        request = VisitorRegistrationRequest(
            name="Alice Johnson",
            purpose="Technical consultation",
            company="DataFlow Analytics",
            appointment_with="Bob Smith",
            expected_duration="2 hours"
        )
        
        assert request.name == "Alice Johnson"
        assert request.purpose == "Technical consultation"
        assert request.company == "DataFlow Analytics"
        assert request.appointment_with == "Bob Smith"
        assert request.expected_duration == "2 hours"


class TestChatRequest:
    """Test ChatRequest model"""
    
    def test_chat_request(self):
        """Test creating a chat request"""
        request = ChatRequest(
            visitor_id="visitor-456",
            message="Can you help me find the conference room?",
            context={"location": "lobby", "time": "morning"}
        )
        
        assert request.visitor_id == "visitor-456"
        assert request.message == "Can you help me find the conference room?"
        assert request.context["location"] == "lobby"
        assert request.context["time"] == "morning"
    
    def test_chat_request_without_visitor_id(self):
        """Test chat request without visitor ID"""
        request = ChatRequest(
            message="What are the visiting hours?"
        )
        
        assert request.visitor_id is None
        assert request.message == "What are the visiting hours?"
        assert request.context == {}


if __name__ == "__main__":
    pytest.main([__file__])
