# HISS Database Integration Guide

## 🗄️ **Neon PostgreSQL Integration**

HISS now uses **Neon PostgreSQL** as the primary database for storing all user interactions, visitor data, and system information.

## 📊 **Database Schema**

### **Core Tables**

#### **Users Table**
Stores admin hierarchy and user management:
```sql
- id (UUID, Primary Key)
- username (String, Unique)
- email (String, Unique) 
- full_name (String)
- role (admin, subadmin, visitor, bot)
- permissions (JSON Array)
- password_hash (String)
- created_at (DateTime)
- last_login (DateTime)
```

#### **Visitors Table**
Stores all visitor information:
```sql
- id (UUID, Primary Key)
- name (String)
- purpose (String)
- company (String)
- contact_email (String)
- contact_phone (String)
- status (registered, checked_in, denied, etc.)
- created_at (DateTime)
```

#### **Conversation Messages Table**
Stores all conversation interactions:
```sql
- id (UUID, Primary Key)
- session_id (UUID, Foreign Key)
- visitor_id (UUID, Foreign Key)
- role (visitor, system, security_guard, admin, subadmin)
- content (Text)
- timestamp (DateTime)
- location (outdoor, indoor, admin_panel)
- intent_detected (String)
- sentiment (String)
- metadata (JSON)
```

#### **Outdoor Interactions Table**
Tracks outdoor visitor interactions:
```sql
- id (UUID, Primary Key)
- visitor_name (String)
- interaction_start (DateTime)
- interaction_end (DateTime)
- location (front_door, etc.)
- purpose_stated (String)
- permission_needed (Boolean)
- final_outcome (allowed, denied, abandoned)
- conversation_summary (Text)
```

#### **Permission Requests Table**
Manages SubAdmin approval workflow:
```sql
- id (UUID, Primary Key)
- visitor_id (UUID)
- visitor_name (String)
- visitor_purpose (String)
- bot_recommendation (String)
- bot_confidence (Float)
- status (pending, approved, denied)
- reviewed_by (UUID, Foreign Key to Users)
- reviewed_at (DateTime)
- decision_reason (String)
```

## 🔧 **Database Configuration**

### **Connection String**
```env
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
```

### **Connection Features**
- **SSL Required**: Secure encrypted connections
- **Connection Pooling**: Optimized for concurrent access
- **Async Support**: Full async/await pattern support
- **Auto-reconnection**: Handles connection drops gracefully

## 🚀 **Setup Instructions**

### **1. Install Dependencies**
```bash
pip install asyncpg sqlalchemy alembic
```

### **2. Configure Environment**
Add to your `.env` file:
```env
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
```

### **3. Initialize Database**
```bash
python scripts/init_database.py
```

This will:
- ✅ Create all database tables
- ✅ Set up relationships and indexes
- ✅ Create default admin users
- ✅ Verify connection

### **4. Start System**
```bash
python main.py
```

## 📈 **Database Operations**

### **Visitor Registration**
```python
# Before (in-memory)
visitors_db[visitor.id] = visitor

# After (database)
visitor = await VisitorService.create_visitor(db, visitor_data)
```

### **Conversation Storage**
```python
# Store message in database
message = await ConversationService.create_message(db, {
    "session_id": session_id,
    "visitor_id": visitor_id,
    "role": "visitor",
    "content": "Hello, I want to visit John",
    "location": "outdoor",
    "intent_detected": "home_visit"
})
```

### **Permission Requests**
```python
# Create permission request
request = await PermissionRequestService.create_request(db, {
    "visitor_id": visitor_id,
    "visitor_name": "Mike",
    "visitor_purpose": "Visit friend John",
    "bot_recommendation": "APPROVE",
    "bot_confidence": 0.85
})
```

### **Admin Analytics**
```python
# Get all conversations for admin
conversations = await ConversationService.get_all_conversations(
    db, limit=100, date_from=start_date
)
```

## 🔍 **Data Persistence**

### **What's Stored in Database:**
- ✅ **All visitor registrations** and status updates
- ✅ **Complete conversation history** across all interactions
- ✅ **Outdoor interaction sessions** with full context
- ✅ **Permission requests and decisions** with audit trail
- ✅ **Admin actions and system events** for compliance
- ✅ **User management and authentication** data
- ✅ **Security decisions and recommendations** with confidence scores

### **Real-time Features:**
- ✅ **Live conversation storage** as messages are sent
- ✅ **Instant permission request creation** when needed
- ✅ **Real-time status updates** for visitors and interactions
- ✅ **Immediate admin action logging** for audit trails

## 🔐 **Security Features**

### **Data Protection:**
- **Encrypted connections** with SSL/TLS
- **Password hashing** for user authentication
- **Role-based access control** at database level
- **Audit logging** for all sensitive operations

### **Backup & Recovery:**
- **Neon automatic backups** with point-in-time recovery
- **Connection pooling** for high availability
- **Graceful error handling** with retry logic

## 📊 **Performance Optimization**

### **Database Indexes:**
- Primary keys on all tables for fast lookups
- Foreign key indexes for relationship queries
- Timestamp indexes for chronological queries
- Composite indexes for complex filtering

### **Connection Management:**
- **Connection pooling** (10 connections, 20 max overflow)
- **Connection recycling** every 5 minutes
- **Pre-ping validation** to handle stale connections
- **Async session management** for concurrent requests

## 🔄 **Migration from In-Memory**

### **Before (In-Memory Storage):**
```python
# Global dictionaries
visitors_db: Dict[str, VisitorInfo] = {}
conversations_db: Dict[str, List[ConversationMessage]] = {}
decisions_db: Dict[str, SecurityDecision] = {}
```

### **After (Database Storage):**
```python
# Database services with async operations
visitor = await VisitorService.create_visitor(db, visitor_data)
messages = await ConversationService.get_all_conversations(db)
decision = await SecurityDecisionService.create_decision(db, decision_data)
```

## 🎯 **Benefits of Database Integration**

### **✅ Data Persistence**
- All data survives system restarts
- No data loss during updates or crashes
- Complete historical records maintained

### **✅ Scalability**
- Handles thousands of concurrent visitors
- Efficient querying with proper indexing
- Connection pooling for high throughput

### **✅ Admin Features**
- Complete conversation history access
- Advanced filtering and search capabilities
- Real-time analytics and reporting

### **✅ Compliance**
- Full audit trails for all actions
- Secure data storage with encryption
- Role-based access control

### **✅ Real-time Operations**
- Instant permission request notifications
- Live conversation updates
- Real-time system monitoring

## 🛠️ **Troubleshooting**

### **Connection Issues:**
```bash
# Test database connection
python -c "
import asyncio
from src.database.config import check_db_connection
print('Connected:', asyncio.run(check_db_connection()))
"
```

### **Table Creation Issues:**
```bash
# Reinitialize database
python scripts/init_database.py
```

### **Performance Issues:**
- Check connection pool settings in `src/database/config.py`
- Monitor query performance with SQL logging
- Verify indexes are properly created

## 📈 **Monitoring**

### **Database Health:**
- Connection pool status
- Query execution times
- Error rates and retry counts
- Active session monitoring

### **Data Growth:**
- Table size monitoring
- Index usage statistics
- Query performance metrics
- Storage utilization tracking

The database integration provides a robust, scalable foundation for the HISS system with complete data persistence and real-time capabilities! 🗄️
